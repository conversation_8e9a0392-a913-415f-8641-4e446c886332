{"name": "bakery-management-md3", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "type-check": "tsc --noEmit"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "expo": "~53.0.9", "expo-barcode-scanner": "^13.0.1", "expo-camera": "~16.1.6", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "~17.1.6", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-paper": "^5.14.5", "react-native-print": "^0.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "supports-hyperlinks": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3"}, "private": true}