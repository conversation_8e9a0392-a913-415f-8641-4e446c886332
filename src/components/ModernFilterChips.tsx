// @ts-nocheck
import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Chip, Text } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';

const ModernFilterChips = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  showCounts = false,
  data = [],
  countField = '',
  style = {},
  chipStyle = {},
  horizontal = true,
  multiSelect = false,
  selectedFilters = [],
}) => {
  const { theme } = useTheme();

  const getFilterCount = (filter) => {
    if (!showCounts || !data.length || !countField) return 0;
    if (filter === 'All') return data.length;
    return data.filter(item => item[countField] === filter).length;
  };

  const isSelected = (filter) => {
    if (multiSelect) {
      return selectedFilters.includes(filter);
    }
    return selectedFilter === filter;
  };

  const handleFilterPress = (filter) => {
    if (multiSelect) {
      const newFilters = selectedFilters.includes(filter)
        ? selectedFilters.filter(f => f !== filter)
        : [...selectedFilters, filter];
      onFilterChange(newFilters);
    } else {
      onFilterChange(filter);
    }
  };

  const renderChip = (filter, index) => {
    const selected = isSelected(filter);
    const count = getFilterCount(filter);

    return (
      <Chip
        key={index}
        mode={selected ? 'flat' : 'outlined'}
        selected={selected}
        onPress={() => handleFilterPress(filter)}
        style={[
          styles.chip,
          {
            backgroundColor: selected 
              ? theme.colors.primaryContainer 
              : theme.colors.surface,
            borderColor: selected 
              ? theme.colors.primary 
              : theme.colors.outline,
            borderWidth: 1,
          },
          chipStyle,
        ]}
        textStyle={{
          color: selected 
            ? theme.colors.onPrimaryContainer 
            : theme.colors.onSurface,
          fontWeight: selected ? '600' : '500',
          fontSize: 14,
        }}
        compact
      >
        {filter}
        {showCounts && count > 0 && (
          <Text style={{
            color: selected 
              ? theme.colors.onPrimaryContainer 
              : theme.colors.onSurfaceVariant,
            fontSize: 12,
            fontWeight: '500',
            marginLeft: 4,
          }}>
            ({count})
          </Text>
        )}
      </Chip>
    );
  };

  if (horizontal) {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[styles.horizontalContainer, style]}
        style={styles.scrollView}
      >
        {filters.map(renderChip)}
      </ScrollView>
    );
  }

  return (
    <View style={[styles.verticalContainer, style]}>
      {filters.map(renderChip)}
    </View>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flexGrow: 0,
  },
  horizontalContainer: {
    paddingHorizontal: 16,
    gap: 8,
  },
  verticalContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 8,
  },
  chip: {
    borderRadius: 20,
    height: 36,
    marginVertical: 0,
  },
});

export default ModernFilterChips;
