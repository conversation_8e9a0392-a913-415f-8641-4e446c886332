// @ts-nocheck
/**
 * AppLayout - Main layout wrapper with header and navbar
 * Provides unified layout structure for all screens
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from './CommonHeader';
import BottomNavBar from './BottomNavBar';

const AppLayout = ({
  // CommonHeader props
  title,
  subtitle,
  searchPlaceholder,
  searchValue,
  onSearchChange,
  showHeader = true,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  notificationCount = 3,
  onNotificationPress,
  onProfilePress,
  headerBackgroundColor,
  headerElevation = 4,
  headerStyle,

  // BottomNavBar props
  navigation,
  currentRoute,
  onPlusPress,
  showBottomNav = true,
  bottomNavStyle,
  bottomNavBackgroundColor,
  bottomNavElevation = 8,

  // Layout props
  children,
  contentStyle,
  backgroundColor,

  // Advanced props
  fullScreen = false,
}) => {
  const { theme } = useTheme();

  const layoutBackgroundColor = backgroundColor || theme.colors.background;

  if (fullScreen) {
    return (
      <View style={[styles.fullScreenContainer, { backgroundColor: layoutBackgroundColor }]}>
        {children}
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: layoutBackgroundColor }]}>
      {/* Common Header */}
      {showHeader && (
        <CommonHeader
          title={title}
          subtitle={subtitle}
          searchPlaceholder={searchPlaceholder}
          searchValue={searchValue}
          onSearchChange={onSearchChange}
          showSearch={showSearch}
          showNotifications={showNotifications}
          showProfile={showProfile}
          notificationCount={notificationCount}
          onNotificationPress={onNotificationPress}
          onProfilePress={onProfilePress}
          backgroundColor={headerBackgroundColor}
          elevation={headerElevation}
          style={headerStyle}
        />
      )}

      {/* Main Content */}
      <View style={[styles.content, contentStyle]}>
        {children}
      </View>

      {/* Bottom Navigation */}
      {showBottomNav && (
        <BottomNavBar
          navigation={navigation}
          currentRoute={currentRoute}
          onPlusPress={onPlusPress}
          backgroundColor={bottomNavBackgroundColor}
          elevation={bottomNavElevation}
          style={bottomNavStyle}
        />
      )}
    </View>
  );
};

// Pre-configured layout variants for common use cases
export const DashboardLayout = (props) => (
  <AppLayout
    currentRoute="Dashboard"
    title="Dashboard"
    subtitle="Welcome back!"
    searchPlaceholder="Search orders, products, customers..."
    {...props}
  />
);

export const ProductsLayout = (props) => (
  <AppLayout
    currentRoute="Products"
    title="Products"
    subtitle="Manage your inventory"
    searchPlaceholder="Search products..."
    {...props}
  />
);

export const OrdersLayout = (props) => (
  <AppLayout
    currentRoute="Orders"
    title="Orders"
    subtitle="Track and manage orders"
    searchPlaceholder="Search orders..."
    {...props}
  />
);

export const ScanLayout = (props) => (
  <AppLayout
    currentRoute="Scan"
    title="Scan Code"
    subtitle="QR & Barcode Scanner"
    showBottomNav={false}
    showSearch={false}
    {...props}
  />
);

export const FinancialLayout = (props) => (
  <AppLayout
    currentRoute="Financial"
    title="Financial"
    subtitle="Revenue and analytics"
    showSearch={false}
    {...props}
  />
);

export const SettingsLayout = (props) => (
  <AppLayout
    currentRoute="Settings"
    title="Settings"
    subtitle="App preferences"
    showSearch={false}
    {...props}
  />
);

// Layout for screens that need back navigation
export const DetailLayout = (props) => (
  <AppLayout
    showBottomNav={false}
    showSearch={false}
    {...props}
  />
);

// Layout for modal-like screens
export const ModalLayout = (props) => (
  <AppLayout
    showBottomNav={false}
    showSearch={false}
    showNotifications={false}
    {...props}
  />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fullScreenContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default AppLayout;
