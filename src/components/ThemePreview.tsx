// @ts-nocheck
/**
 * ThemePreview - Component to preview the current theme
 * Shows sample UI elements with the current theme styling
 */

import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Button, Divider, Surface, Chip } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';
import UnifiedCard from './UnifiedCard';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ThemePreview = () => {
  const { theme, isDarkMode } = useTheme();
  
  // Sample data for preview
  const sampleCardData = {
    title: 'Chocolate Croissant',
    subtitle: 'Freshly baked pastry',
    description: 'Buttery, flaky croissant filled with rich chocolate',
    price: 4.99,
    status: 'Popular',
    icon: 'food-croissant',
    image: 'https://images.unsplash.com/photo-1555507036-ab1f4038808a?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80',
  };
  
  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.header}>
        <Text 
          variant="headlineSmall"
          style={[styles.title, { color: theme.colors.onSurface }]}
        >
          Theme Preview: Default
        </Text>
        <Text 
          variant="bodyMedium"
          style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}
        >
          {isDarkMode ? 'Dark Mode' : 'Light Mode'}
        </Text>
      </View>
      
      <Divider style={styles.divider} />
      
      <ScrollView style={styles.previewContent}>
        {/* Color Palette */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Color Palette
          </Text>
          
          <View style={styles.colorGrid}>
            {[
              { name: 'Primary', color: theme.colors.primary },
              { name: 'Secondary', color: theme.colors.secondary },
              { name: 'Surface', color: theme.colors.surface },
              { name: 'Background', color: theme.colors.background },
              { name: 'Error', color: theme.colors.error },
            ].map((item, index) => (
              <View key={index} style={styles.colorItem}>
                <View 
                  style={[
                    styles.colorSwatch, 
                    { backgroundColor: item.color }
                  ]} 
                />
                <Text style={[styles.colorName, { color: theme.colors.onSurfaceVariant }]}>
                  {item.name}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* UI Components */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            UI Components
          </Text>
          
          <View style={styles.componentRow}>
            <Button 
              mode="contained" 
              onPress={() => {}} 
              style={styles.button}
            >
              Primary
            </Button>
            
            <Button 
              mode="outlined" 
              onPress={() => {}} 
              style={styles.button}
            >
              Secondary
            </Button>
            
            <Button 
              mode="text" 
              onPress={() => {}} 
              style={styles.button}
            >
              Text
            </Button>
          </View>
          
          <View style={styles.chipRow}>
            <Chip 
              icon="check" 
              onPress={() => {}}
              style={styles.chip}
            >
              Selected
            </Chip>
            
            <Chip 
              icon="tag" 
              onPress={() => {}}
              style={styles.chip}
            >
              Tag
            </Chip>
            
            <Chip 
              icon="filter" 
              onPress={() => {}}
              style={styles.chip}
            >
              Filter
            </Chip>
          </View>
        </View>
        
        {/* Card Preview */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Card Component
          </Text>
          
          <UnifiedCard
            {...sampleCardData}
            actions={[
              { icon: 'pencil', onPress: () => {} },
              { icon: 'delete', onPress: () => {} },
            ]}
            primaryAction={{
              title: 'Order',
              onPress: () => {},
            }}
            secondaryAction={{
              title: 'View',
              onPress: () => {},
            }}
          />
        </View>
      </ScrollView>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  header: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  divider: {
    height: 1,
  },
  previewContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  colorItem: {
    width: '20%',
    alignItems: 'center',
    padding: 8,
  },
  colorSwatch: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  colorName: {
    fontSize: 12,
    textAlign: 'center',
  },
  componentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
  chipRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  chip: {
    margin: 4,
  },
});

export default ThemePreview;