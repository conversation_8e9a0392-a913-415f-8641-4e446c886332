// @ts-nocheck
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';

const BottomSheetProvider = ({ children }) => {
  return (
    <View style={styles.container}>
      <BottomSheetModalProvider>
        {children}
      </BottomSheetModalProvider>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
});

export default BottomSheetProvider;
