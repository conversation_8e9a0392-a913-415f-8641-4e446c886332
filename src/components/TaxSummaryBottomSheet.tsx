// @ts-nocheck
/**
 * TaxSummaryBottomSheet - Unified Tax Summary
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useState, forwardRef, useImperativeHandle, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Alert, Keyboard } from 'react-native';
import {
  Text,
  Button,
  Card,
  DataTable,
  Divider,
  Chip,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { FINANCIAL_CONFIG } from '../config/constants';
import { useTheme } from '../context/ThemeContext';

const TaxSummaryBottomSheet = forwardRef(({ data }, ref) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getTaxStatus = (amount) => {
    if (amount === 0) {
      return { status: 'No Tax Due', color: '#4CAF50', icon: 'check-circle' };
    } else if (amount < 1000) {
      return { status: 'Low Tax Liability', color: '#FF9800', icon: 'alert-circle' };
    } else {
      return { status: 'High Tax Liability', color: '#F44336', icon: 'alert' };
    }
  };

  const exportTaxReport = () => {
    Alert.alert(
      'Export Tax Report',
      'This will generate a detailed tax report for your accountant.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // Export functionality would go here
            console.log('Export tax report');
            Alert.alert('Success', 'Tax report exported successfully!');
          },
        },
      ]
    );
  };

  const scheduleTaxReminder = () => {
    Alert.alert(
      'Tax Reminder',
      'Set up automatic reminders for tax filing deadlines?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Set Reminder',
          onPress: () => {
            // Reminder functionality would go here
            Alert.alert('Success', 'Tax reminders have been set up!');
          },
        },
      ]
    );
  };

  if (!data) return null;

  const taxStatus = getTaxStatus(data.totalTaxLiability);

  const getTaxInfo = () => {
    return `${formatCurrency(data.totalTaxLiability)} liability • ${taxStatus.status}`;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                <Icon name="calculator" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Tax Summary
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {new Date(data.period.startDate).toLocaleDateString()} - {new Date(data.period.endDate).toLocaleDateString()} • {getTaxInfo()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >

        {/* Tax Status Card */}
        <Card style={[styles.statusCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <Icon name={taxStatus.icon} size={32} color={taxStatus.color} />
              <View style={styles.statusInfo}>
                <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                  {formatCurrency(data.totalTaxLiability)}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Total Tax Liability
                </Text>
              </View>
            </View>
            <Chip
              icon={taxStatus.icon}
              style={{
                backgroundColor: taxStatus.color + '20',
                alignSelf: 'flex-start',
                marginTop: 12,
              }}
              textStyle={{ color: taxStatus.color }}
            >
              {taxStatus.status}
            </Chip>
          </Card.Content>
        </Card>

        {/* Sales Tax Details */}
        <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.taxHeader}>
              <Icon name="cash-register" size={24} color="#2196F3" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Sales Tax
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Taxable Sales</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.salesTax.taxableAmount)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Rate</DataTable.Cell>
                <DataTable.Cell numeric>{formatPercentage(data.salesTax.rate)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Amount</DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: '#2196F3', fontWeight: '600' }}>
                    {formatCurrency(data.salesTax.amount)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <View style={styles.taxNote}>
              <Icon name="information" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 4 }}>
                Sales tax is collected on all taxable sales and must be remitted to tax authorities
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Income Tax Details */}
        <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.taxHeader}>
              <Icon name="chart-line" size={24} color="#4CAF50" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Income Tax
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Taxable Income</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.incomeTax.taxableAmount)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Rate</DataTable.Cell>
                <DataTable.Cell numeric>{formatPercentage(data.incomeTax.rate)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Amount</DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: '#4CAF50', fontWeight: '600' }}>
                    {formatCurrency(data.incomeTax.amount)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <View style={styles.taxNote}>
              <Icon name="information" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 4 }}>
                Income tax is calculated on net profit after deducting business expenses
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Tax Calendar */}
        <Card style={[styles.calendarCard, { backgroundColor: theme.colors.surfaceVariant }]}>
          <Card.Content>
            <View style={styles.calendarHeader}>
              <Icon name="calendar-clock" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Important Tax Dates
              </Text>
            </View>

            <View style={styles.datesList}>
              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Quarterly Sales Tax
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: Last day of month following quarter
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#FF9800' + '20' }}>
                  <Text style={{ color: '#FF9800' }}>Quarterly</Text>
                </Chip>
              </View>

              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Annual Income Tax
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: March 15th (Corporate) / April 15th (Individual)
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#4CAF50' + '20' }}>
                  <Text style={{ color: '#4CAF50' }}>Annual</Text>
                </Chip>
              </View>

              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Estimated Tax Payments
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: 15th of Jan, Apr, Jun, Sep
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#2196F3' + '20' }}>
                  <Text style={{ color: '#2196F3' }}>Quarterly</Text>
                </Chip>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Tax Summary Table */}
        <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
          <Card.Content>
            <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, marginBottom: 16 }}>
              Tax Summary
            </Text>

            <View style={styles.summaryRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Sales Tax
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                {formatCurrency(data.salesTax.amount)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Income Tax
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                {formatCurrency(data.incomeTax.amount)}
              </Text>
            </View>

            <Divider style={[styles.divider, { backgroundColor: theme.colors.onPrimaryContainer + '30' }]} />

            <View style={styles.summaryRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Total Tax Liability
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                {formatCurrency(data.totalTaxLiability)}
              </Text>
            </View>
              </Card.Content>
            </Card>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.actionContainer}>
              <Button
                mode="outlined"
                onPress={scheduleTaxReminder}
                icon="bell"
                style={styles.actionButton}
              >
                Set Reminders
              </Button>
              <Button
                mode="contained"
                onPress={exportTaxReport}
                icon="download"
                style={styles.actionButton}
              >
                Export Report
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  statusCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: 16,
    flex: 1,
  },
  taxCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  taxHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  taxNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 12,
    padding: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  calendarCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  datesList: {
    gap: 12,
  },
  dateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateInfo: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default TaxSummaryBottomSheet;
