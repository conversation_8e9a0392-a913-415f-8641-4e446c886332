// @ts-nocheck
/**
 * StatsDetailsBottomSheet - Unified Stats Details
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useMemo, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Divider,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';

const StatsDetailsBottomSheet = ({
  bottomSheetRef,
  statsType,
  data,
  title,
  icon,
  color
}) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const snapPoints = useMemo(() => ['80%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  if (!statsType || !data) return null;

  const renderSalesDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="chart-line" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Sales Breakdown
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              ৳${data.todaysSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Today's Sales
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              ৳{data.weekSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              This Week
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              ৳{data.monthSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              This Month
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              ৳{data.totalRevenue?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Revenue
            </Text>
          </View>
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="trending-up" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Performance Metrics
          </Text>
        </View>
        <View style={styles.metricsList}>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Average Order Value:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              ৳${data.avgOrderValue?.toFixed(2) || '0.00'}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders Today:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersToday || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Completion Rate:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.completionRate?.toFixed(1) || '0.0'}%
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderOrdersDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="clipboard-list" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Status Breakdown
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.pending || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Pending
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              {data.inProgress || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              In Progress
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              {data.completed || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Completed
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.error, fontWeight: '700' }}>
              {data.cancelled || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Cancelled
            </Text>
          </View>
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="clock-outline" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Recent Activity
          </Text>
        </View>
        <View style={styles.metricsList}>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders Today:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersToday || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders This Week:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersWeek || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Average Processing Time:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.avgProcessingTime || 'N/A'}
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderProductsDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="food-croissant" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Categories
          </Text>
        </View>
        <View style={styles.metricsList}>
          {data.categories?.map((category, index) => (
            <View key={index} style={styles.metricRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {category.name}:
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {category.count} items
              </Text>
            </View>
          )) || (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No category data available
            </Text>
          )}
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="star" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Top Performers
          </Text>
        </View>
        <View style={styles.metricsList}>
          {data.topProducts?.slice(0, 5).map((product, index) => (
            <View key={index} style={styles.metricRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {product.name}:
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {product.sales || 0} sold
              </Text>
            </View>
          )) || (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No sales data available
            </Text>
          )}
        </View>
      </Surface>
    </View>
  );

  const renderCustomersDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="account-group" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Insights
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.newCustomers || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              New This Month
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              {data.returningCustomers || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Returning
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              ৳{data.avgCustomerValue?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Avg. Value
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.loyaltyRate?.toFixed(1) || '0.0'}%
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Loyalty Rate
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderContent = () => {
    switch (statsType) {
      case 'sales':
        return renderSalesDetails();
      case 'orders':
        return renderOrdersDetails();
      case 'products':
        return renderProductsDetails();
      case 'customers':
        return renderCustomersDetails();
      default:
        return <Text>No data available</Text>;
    }
  };

  const getStatsInfo = () => {
    switch (statsType) {
      case 'sales':
        return `৳${data.todaysSales?.toFixed(2) || '0.00'} today • ${data.totalOrders || 0} orders`;
      case 'orders':
        return `${data.pending || 0} pending • ${data.completed || 0} completed`;
      case 'products':
        return `${data.totalProducts || 0} products • ${data.lowStock || 0} low stock`;
      case 'customers':
        return `${data.newCustomers || 0} new • ${data.totalCustomers || 0} total`;
      default:
        return 'Statistics overview';
    }
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: (color || theme.colors.primary) + '15' }]}>
                <Icon name={icon || 'chart-box'} size={24} color={color || theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  {title || 'Statistics'}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {getStatsInfo()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {renderContent()}

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 20, 40) }} />
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricsList: {
    gap: 6,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
});

export default StatsDetailsBottomSheet;
