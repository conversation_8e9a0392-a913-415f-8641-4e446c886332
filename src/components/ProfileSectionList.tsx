import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import { ProfileSectionListProps } from '../types';

const ProfileSectionList: React.FC<ProfileSectionListProps> = ({ title, items = [], footer }) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
      {title && (
        <Text style={[styles.sectionTitle, { color: theme.colors.onSurfaceVariant }]}> 
          {title}
        </Text>
      )}
      {items.map((item, idx) => (
        <TouchableOpacity
          key={item.label}
          style={[
            styles.item,
            { borderBottomColor: idx === items.length - 1 ? 'transparent' : theme.colors.outline + '22' }
          ]}
          onPress={item.onPress}
          activeOpacity={0.7}
        >
          <Icon name={item.icon} size={24} color={theme.colors.onSurfaceVariant} style={styles.icon} />
          <Text style={[styles.label, { color: theme.colors.onSurface }]}>{item.label}</Text>
          {item.rightComponent ? item.rightComponent : (
            <Icon name="chevron-right" size={24} color={theme.colors.onSurfaceVariant} style={styles.chevron} />
          )}
        </TouchableOpacity>
      ))}
      {footer && <View style={styles.footer}>{footer}</View>}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    borderRadius: 0,
    padding: 8,
    marginVertical: 4, // Reduce gap between sections
    marginHorizontal: 0,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 8,
    marginLeft: 16,
    opacity: 0.7,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  icon: {
    marginRight: 16,
  },
  label: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  chevron: {
    marginLeft: 8,
  },
  footer: {
    marginTop: 8,
    alignItems: 'center',
  },
});

export default ProfileSectionList; 