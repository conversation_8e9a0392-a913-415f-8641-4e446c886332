import React, { useState, useEffect } from 'react';
import { ViewStyle } from 'react-native';
import { TextInput, Text } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';

type InputType = 'text' | 'email' | 'number' | 'password' | 'search' | 'date';

interface Validators {
  [key: string]: (value: string, minLength?: number) => string;
}

const validators: Validators = {
  email: (value: string) => !value || /\S+@\S+\.\S+/.test(value) ? '' : 'Invalid email address',
  number: (value: string) => value === '' || (!isNaN(Number(value)) && isFinite(Number(value))) ? '' : 'Invalid number',
  password: (value: string, minLength: number = 8) => value.length >= minLength ? '' : `Password must be at least ${minLength} characters`,
  text: (value: string) => '',
};

interface UnifiedTextInputProps {
  label: string;
  value: string;
  onChangeText: (value: string) => void;
  type?: InputType;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  validate?: (value: string) => string;
  error?: string;
  onBlur?: (e: any) => void;
  style?: ViewStyle;
  rightAffix?: string;
  [key: string]: any;
}

/**
 * UnifiedTextInput props:
 * - rightAffix: string (optional) - renders a TextInput.Affix with the given text on the right
 */
const UnifiedTextInput: React.FC<UnifiedTextInputProps> = ({
  label,
  value,
  onChangeText,
  type = 'text',
  required = false,
  minLength,
  maxLength,
  validate,
  error: externalError,
  onBlur,
  style,
  rightAffix,
  ...rest
}) => {
  const [error, setError] = useState('');
  const [touched, setTouched] = useState(false);
  const { theme } = useTheme();

  useEffect(() => {
    if (externalError !== undefined) setError(externalError);
  }, [externalError]);

  const runValidation = (val: string): string => {
    let err = '';
    if (required && (!val || val.trim() === '')) err = `${label || 'This field'} is required`;
    else if (type && validators[type]) err = validators[type](val, minLength);
    if (!err && minLength && val.length < minLength) err = `${label || 'This field'} must be at least ${minLength} characters`;
    if (!err && maxLength && val.length > maxLength) err = `${label || 'This field'} must be at most ${maxLength} characters`;
    if (!err && typeof validate === 'function') err = validate(val) || '';
    setError(err);
    return err;
  };

  const handleChange = (val: string): void => {
    onChangeText(val);
    if (touched) runValidation(val);
  };

  const handleBlur = (e: any): void => {
    setTouched(true);
    runValidation(value);
    if (onBlur) onBlur(e);
  };

  return (
    <>
      <TextInput
        label={label}
        value={value}
        onChangeText={handleChange}
        onBlur={handleBlur}
        mode="outlined"
        error={!!error}
        style={[{ marginBottom: 12 }, style]}
        secureTextEntry={type === 'password'}
        keyboardType={type === 'email' ? 'email-address' : type === 'number' ? 'numeric' : 'default'}
        autoCapitalize={type === 'email' ? 'none' : 'sentences'}
        right={rightAffix ? <TextInput.Affix text={rightAffix} /> : undefined}
        {...rest}
      />
      {!!error && (
        <Text style={{ color: '#EF4444', marginTop: -8, marginBottom: 8 }}>{error}</Text>
      )}
    </>
  );
};

export default UnifiedTextInput; 