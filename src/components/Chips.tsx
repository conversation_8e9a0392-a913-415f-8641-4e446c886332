/**
 * Chips - Minimal, flat chips component for all screens
 */

import React from 'react';
import { View, StyleSheet, ScrollView, ViewStyle } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { Chip } from 'react-native-paper';

interface FilterItem {
  id?: string | number;
  label?: string;
  icon?: string;
  value?: string | number;
}

interface ChipsProps {
  filters?: (FilterItem | string)[];
  selectedFilter?: string | number;
  onFilterChange?: (filter: string | number) => void;
  withIcons?: boolean;
  showCounts?: boolean;
  data?: any[];
  countField?: string;
  style?: ViewStyle;
  chipStyle?: ViewStyle;
  icons?: Record<string, string>;
}

const Chips: React.FC<ChipsProps> = ({
  filters = [],
  selectedFilter,
  onFilterChange,
  withIcons = false,
  showCounts = false,
  data = [],
  countField = 'status',
  style,
  chipStyle,
  icons = {},
}) => {
  const { theme } = useTheme();

  // Defensive: filter out invalid filters
  const validFilters = filters.filter((filter: FilterItem | string) => {
    if (typeof filter === 'string' && filter.trim() !== '') return true;
    if (typeof filter === 'object' && filter !== null && typeof (filter as FilterItem).label === 'string' && (filter as FilterItem).label!.trim() !== '') return true;
    return false;
  });

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.chipContainer, style]}
    >
      {validFilters.map((filter: FilterItem | string) => {
        const id = (typeof filter === 'object' && filter !== null && (filter as FilterItem).id) ? (filter as FilterItem).id : filter;
        const label = (typeof filter === 'object' && filter !== null && (filter as FilterItem).label) ? (filter as FilterItem).label : filter;
        const icon = withIcons ? ((filter as FilterItem).icon || icons[id as string]) : undefined;
        let count = null;
        if (showCounts && data.length) {
          if (label === 'All') {
            count = data.length;
          } else {
            count = data.filter(item => item[countField] === label).length;
          }
        }
        return (
          <Chip
            key={String(id)}
            selected={selectedFilter === id}
            onPress={() => onFilterChange?.(id as string | number)}
            icon={icon}
            style={[
              styles.chip,
              chipStyle,
              selectedFilter === id && { backgroundColor: theme.colors.primaryContainer },
            ]}
            textStyle={{
              color: selectedFilter === id ? theme.colors.onPrimaryContainer : theme.colors.onSurface,
              fontWeight: selectedFilter === id ? '600' : '400',
            }}
            mode={selectedFilter === id ? 'flat' : 'outlined'}
            compact
          >
            {count !== null ? `${label}・${count}` : String(label ?? '')}
          </Chip>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8, // Semi-rounded corners
    height: 36,
  },
});

export default Chips;
