/**
 * UnifiedStatusIcon - Consistent status indicator component
 * Provides standardized status icons with colors and labels
 */

import React from 'react';
import { View, StyleSheet, Text, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

type StatusType = 'pending' | 'processing' | 'ready' | 'completed' | 'cancelled' | 'success' | 'error' | 'warning' | 'info';
type LabelPosition = 'right' | 'bottom';
type Variant = 'default' | 'filled' | 'outlined';

interface UnifiedStatusIconProps {
  // Status
  status: StatusType;

  // Display options
  size?: number;
  showLabel?: boolean;
  labelPosition?: LabelPosition;

  // Custom overrides
  customIcon?: string;
  customColor?: string;
  customLabel?: string;

  // Layout
  style?: ViewStyle;
  iconStyle?: ViewStyle;
  labelStyle?: TextStyle;

  // Variants
  variant?: Variant;
}

const UnifiedStatusIcon: React.FC<UnifiedStatusIconProps> = ({
  // Status
  status,

  // Display options
  size = 20,
  showLabel = false,
  labelPosition = 'right',

  // Custom overrides
  customIcon,
  customColor,
  customLabel,

  // Layout
  style,
  iconStyle,
  labelStyle,

  // Variants
  variant = 'default',
}) => {
  const { theme } = useTheme();

  // Status configurations
  const statusConfig = {
    pending: {
      icon: 'clock-outline',
      color: '#FF9800',
      label: 'Pending',
      backgroundColor: '#FF980015',
    },
    processing: {
      icon: 'cog-outline',
      color: '#2196F3',
      label: 'Processing',
      backgroundColor: '#2196F315',
    },
    ready: {
      icon: 'check-circle-outline',
      color: '#4CAF50',
      label: 'Ready',
      backgroundColor: '#4CAF5015',
    },
    completed: {
      icon: 'check-all',
      color: '#8BC34A',
      label: 'Completed',
      backgroundColor: '#8BC34A15',
    },
    cancelled: {
      icon: 'close-circle-outline',
      color: '#F44336',
      label: 'Cancelled',
      backgroundColor: '#F4433615',
    },
    success: {
      icon: 'check-circle',
      color: '#4CAF50',
      label: 'Success',
      backgroundColor: '#4CAF5015',
    },
    error: {
      icon: 'alert-circle',
      color: '#F44336',
      label: 'Error',
      backgroundColor: '#F4433615',
    },
    warning: {
      icon: 'alert',
      color: '#FF9800',
      label: 'Warning',
      backgroundColor: '#FF980015',
    },
    info: {
      icon: 'information',
      color: '#2196F3',
      label: 'Info',
      backgroundColor: '#2196F315',
    },
    active: {
      icon: 'circle',
      color: '#4CAF50',
      label: 'Active',
      backgroundColor: '#4CAF5015',
    },
    inactive: {
      icon: 'circle-outline',
      color: '#9E9E9E',
      label: 'Inactive',
      backgroundColor: '#9E9E9E15',
    },
    draft: {
      icon: 'file-document-outline',
      color: '#9E9E9E',
      label: 'Draft',
      backgroundColor: '#9E9E9E15',
    },
    published: {
      icon: 'file-document',
      color: '#4CAF50',
      label: 'Published',
      backgroundColor: '#4CAF5015',
    },
    archived: {
      icon: 'archive',
      color: '#795548',
      label: 'Archived',
      backgroundColor: '#79554815',
    },
  };

  const config = statusConfig[status] || statusConfig.info;
  
  // Get final values with custom overrides
  const finalIcon = customIcon || config.icon;
  const finalColor = customColor || config.color;
  const finalLabel = customLabel || config.label;

  // Variant styles
  const getVariantStyles = () => {
    const containerSize = size + 8;
    
    switch (variant) {
      case 'filled':
        return {
          container: {
            backgroundColor: config.backgroundColor,
            borderRadius: containerSize / 2,
            width: containerSize,
            height: containerSize,
            justifyContent: 'center',
            alignItems: 'center',
          },
          icon: {},
        };
      
      case 'outlined':
        return {
          container: {
            borderWidth: 1,
            borderColor: finalColor,
            borderRadius: containerSize / 2,
            width: containerSize,
            height: containerSize,
            justifyContent: 'center',
            alignItems: 'center',
          },
          icon: {},
        };
      
      default:
        return {
          container: {},
          icon: {},
        };
    }
  };

  const variantStyles = getVariantStyles();

  const renderIcon = () => (
    <View style={[variantStyles.container as any, iconStyle]}>
      <Icon
        name={finalIcon}
        size={size}
        color={finalColor}
        style={variantStyles.icon}
      />
    </View>
  );

  const renderLabel = () => {
    if (!showLabel) return null;

    return (
      <Text

        style={[
          styles.label,
          {
            color: finalColor,
            marginLeft: labelPosition === 'right' ? 8 : 0,
            marginTop: labelPosition === 'bottom' ? 4 : 0,
          },
          labelStyle,
        ]}
      >
        {finalLabel}
      </Text>
    );
  };

  const containerStyles = [
    styles.container,
    labelPosition === 'bottom' && styles.containerColumn,
    style,
  ];

  return (
    <View style={containerStyles}>
      {renderIcon()}
      {renderLabel()}
    </View>
  );
};

// Preset components for common use cases
export const OrderStatusIcon: React.FC<{ status: string; [key: string]: any }> = ({ status, ...props }) => {
  // Map order status strings to StatusType
  const orderStatusMap: Record<string, StatusType> = {
    'pending': 'pending',
    'confirmed': 'processing',
    'preparing': 'processing',
    'ready': 'ready',
    'delivered': 'completed',
    'cancelled': 'cancelled',
  };

  return (
    <UnifiedStatusIcon
      status={orderStatusMap[status] || 'pending'}
      showLabel={true}
      {...props}
    />
  );
};

export const PaymentStatusIcon: React.FC<{ status: string; [key: string]: any }> = ({ status, ...props }) => {
  const paymentStatusMap: Record<string, StatusType> = {
    'paid': 'completed',
    'unpaid': 'pending',
    'partial': 'processing',
    'refunded': 'cancelled',
  };

  return (
    <UnifiedStatusIcon
      status={paymentStatusMap[status] || 'pending'}
      showLabel={true}
      {...props}
    />
  );
};

export const ProductStatusIcon: React.FC<{ status: string; [key: string]: any }> = ({ status, ...props }) => {
  const productStatusMap: Record<string, StatusType> = {
    'in-stock': 'success',
    'out-of-stock': 'error',
    'low-stock': 'warning',
    'discontinued': 'cancelled',
  };

  return (
    <UnifiedStatusIcon
      status={productStatusMap[status] || 'info'}
      showLabel={true}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  containerColumn: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  label: {
    fontWeight: '500',
  },
});

export default UnifiedStatusIcon;
