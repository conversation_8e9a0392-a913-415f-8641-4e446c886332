// @ts-nocheck
/**
 * SettingsBottomSheet - Unified Settings Panel
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { forwardRef, useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import LoggingService from '../services/LoggingService';
import {
  Text,
  Switch,
  Surface,
  TouchableRipple,
  Divider,
  Button,
  IconButton,
  Portal
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import ThemeSelector from './ThemeSelector';
import ThemePreview from './ThemePreview';

const SettingsBottomSheet = forwardRef((props, ref) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['60%', '90%'], []);

  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    autoBackup: true,
    soundEffects: true,
    analytics: false,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => bottomSheetRef.current?.expand(),
    close: () => bottomSheetRef.current?.close()
  }));

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  const toggleSetting = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSave = () => {
    LoggingService.info('Saving settings', 'SETTINGS', settings);
    bottomSheetRef.current?.close();
  };

  const handleReset = () => {
    setSettings({
      notifications: true,
      darkMode: false,
      autoBackup: true,
      soundEffects: true,
      analytics: false,
    });
  };

  const renderSettingItem = (key, title, subtitle, icon) => (
    <View key={key}>
      <TouchableRipple onPress={() => toggleSetting(key)}>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <View style={[styles.settingIcon, { backgroundColor: theme.colors.primaryContainer }]}>
              <Icon name={icon} size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.settingText}>
              <Text variant="titleMedium" style={[styles.settingTitle, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
              <Text variant="bodySmall" style={[styles.settingSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle}
              </Text>
            </View>
          </View>
          <Switch
            value={settings[key]}
            onValueChange={() => toggleSetting(key)}
          />
        </View>
      </TouchableRipple>
      <Divider style={styles.divider} />
    </View>
  );

  const getActiveSettingsCount = () => {
    return Object.values(settings).filter(Boolean).length;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                <Icon name="cog" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Settings
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {getActiveSettingsCount()}/5 features enabled • App preferences
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Theme Settings */}
            <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
              <View style={styles.sectionHeader}>
                <Icon name="palette" size={20} color={theme.colors.primary} />
                <Text variant="titleMedium" style={styles.sectionTitle}>Theme Settings</Text>
              </View>
              
              <ThemeSelector />
              
              {/* Theme Preview */}
              <Divider style={{ marginVertical: 16, opacity: 0.3 }} />
              <Text variant="titleMedium" style={{ fontWeight: '500', marginBottom: 12 }}>
                Preview
              </Text>
              <ThemePreview />
            </Surface>
            
            {/* App Settings */}
            <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
              <View style={styles.sectionHeader}>
                <Icon name="application-cog" size={20} color={theme.colors.primary} />
                <Text variant="titleMedium" style={styles.sectionTitle}>App Settings</Text>
              </View>

              {renderSettingItem(
                'notifications',
                'Push Notifications',
                'Receive alerts for new orders and updates',
                'bell'
              )}

              {renderSettingItem(
                'soundEffects',
                'Sound Effects',
                'Play sounds for actions and notifications',
                'volume-high'
              )}
            </Surface>

            {/* Data & Privacy */}
            <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
              <View style={styles.sectionHeader}>
                <Icon name="shield-account" size={20} color={theme.colors.secondary} />
                <Text variant="titleMedium" style={styles.sectionTitle}>Data & Privacy</Text>
              </View>

              {renderSettingItem(
                'autoBackup',
                'Auto Backup',
                'Automatically backup data to cloud storage',
                'cloud-upload'
              )}

              {renderSettingItem(
                'analytics',
                'Usage Analytics',
                'Help improve the app by sharing usage data',
                'chart-line'
              )}
            </Surface>

            {/* About Section */}
            <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
              <View style={styles.sectionHeader}>
                <Icon name="information" size={20} color={theme.colors.tertiary} />
                <Text variant="titleMedium" style={styles.sectionTitle}>About</Text>
              </View>

              <View style={styles.aboutContent}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                  Bakery Management App
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
                  Version 1.0.0
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 8 }}>
                  Built with React Native & Material Design 3
                </Text>
              </View>
            </Surface>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={handleReset}
                style={styles.button}
              >
                Reset
              </Button>
              <Button
                mode="contained"
                onPress={handleSave}
                style={styles.button}
              >
                Save Changes
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    lineHeight: 16,
  },
  divider: {
    marginVertical: 4,
    opacity: 0.3,
  },
  aboutContent: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
  },
});

SettingsBottomSheet.displayName = 'SettingsBottomSheet';

export default SettingsBottomSheet;
