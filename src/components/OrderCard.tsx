// @ts-nocheck
/**
 * OrderCard - Final version with dynamic due amount and new badge style.
 * @param {Object} props
 * @param {Object} props.order - The order object to display
 * @param {Function} props.onPress - Callback function when card is pressed
 */

import React, { useMemo, useCallback } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';

const statusColors = {
  PENDING: { bg: '#374151', text: '#fff' },
  READY: { bg: '#166534', text: '#fff' },
  CANCELLED: { bg: '#991b1b', text: '#fff' },
  'IN PROGRESS': { bg: '#1e40af', text: '#fff' },
  PAID: { bg: '#059669', text: '#fff' },
  DELIVERED: { bg: '#059669', text: '#fff' },
};

const OrderCard = ({ order, onPress }) => {
  const { theme } = useTheme();

  const orderData = useMemo(() => {
    const status = order.status?.toUpperCase() || 'PENDING';
    const statusColor = statusColors[status] || { bg: theme.colors.primary, text: '#fff' };
    const itemCount = order.items?.length || 0;
    const isPaid = order.status === 'PAID' || order.status === 'DELIVERED' || order.status === 'READY';
    const dueAmount = order.total - (order.paidAmount || 0);
    return {
      status,
      statusColor,
      itemCount,
      isPaid,
      dueAmount,
    };
  }, [order.status, order.items?.length, order.total, order.paidAmount, theme.colors.primary]);

  const styles = useMemo(() => StyleSheet.create({
    card: {
      backgroundColor: '#181b20',
      borderRadius: 16,
      padding: 18,
      marginBottom: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      elevation: 2,
    },
    left: { flex: 1 },
    orderNumberRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
    orderNumber: { color: 'white', fontWeight: 'bold', fontSize: 20 },
    statusBadge: {
      backgroundColor: orderData.statusColor.bg,
      borderRadius: 8,
      paddingHorizontal: 10,
      paddingVertical: 2,
      alignSelf: 'flex-start',
      marginLeft: 8,
      marginTop: 2,
    },
    statusText: {
      color: orderData.statusColor.text,
      fontWeight: 'bold',
      fontSize: 13,
      letterSpacing: 0.5,
      textTransform: 'uppercase',
    },
    itemsLink: {
      color: '#38bdf8',
      fontWeight: '500',
      fontSize: 15,
      marginTop: 2,
      marginBottom: 6,
    },
    dates: {
      color: '#b0b3b8',
      fontSize: 13,
      marginBottom: 2,
    },
    right: { alignItems: 'flex-end', minWidth: 90 },
    price: {
      color: 'white',
      fontWeight: 'bold',
      fontSize: 22,
      marginBottom: 2,
    },
    dueText: {
      color: '#f43f5e',
      fontWeight: 'bold',
      fontSize: 15,
      marginTop: 2,
    },
    paidText: {
      color: '#22c55e',
      fontWeight: 'bold',
      fontSize: 15,
      marginTop: 2,
      textTransform: 'uppercase',
    },
  }), [orderData, theme]);

  const handlePress = useCallback(() => {
    onPress(order);
  }, [onPress, order]);

  return (
    <TouchableOpacity style={styles.card} activeOpacity={0.85} onPress={handlePress}>
      <View style={styles.left}>
        <View style={styles.orderNumberRow}>
          <Text style={styles.orderNumber}>Order #{order.id}</Text>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>{orderData.status.replace('_', ' ')}</Text>
          </View>
        </View>
        <Text style={styles.itemsLink}>{orderData.itemCount} Items</Text>
        <Text style={styles.dates}>Created: {order.createdAt}</Text>
        <Text style={styles.dates}>Due: {order.dueDate}</Text>
      </View>
      <View style={styles.right}>
        <Text style={styles.price}>{order.total} TK</Text>
        {orderData.isPaid ? (
          <Text style={styles.paidText}>Paid</Text>
        ) : (
          <Text style={styles.dueText}>Due: {orderData.dueAmount} TK</Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default OrderCard; 