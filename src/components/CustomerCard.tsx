// @ts-nocheck
import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Surface } from 'react-native-paper';
import { useTheme } from '../context/ThemeContext';

const CustomerCard = ({ customer, onPress }) => {
  const { theme } = useTheme();

  // Format the join date to be "Mmm d, yyyy"
  const joinedDate = new Date(customer.createdAt || customer.date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });

  // Determine the text for total and active orders
  const totalOrdersText = customer.totalOrders === 1 ? '1 total order' : `${customer.totalOrders || 0} total orders`;
  const activeOrdersText = customer.activeOrders === 1 ? '1 Active order' : `${customer.activeOrders || 0} Active orders`;

  // Determine the color for the active orders text
  const activeOrdersColor = customer.activeOrders > 0 ? '#2dd4bf' : theme?.colors?.onSurfaceVariant;

  // Determine if a status badge for "New" or "VIP" should be shown.
  const customerStatus = useMemo(() => {
    if (customer.isVIP || customer.totalSpent > 50000) {
      return { text: 'VIP', backgroundColor: '#4d443a', color: '#ffb74d' };
    }
    if (customer.totalOrders === 0) {
      return { text: 'New', backgroundColor: theme?.colors?.primaryContainer || '#E3F2FD', color: theme?.colors?.onPrimaryContainer || '#0D47A1' };
    }
    return null;
  }, [customer.isVIP, customer.totalSpent, customer.totalOrders, theme?.colors]);

  return (
    <TouchableOpacity onPress={() => onPress(customer)} activeOpacity={0.8}>
      <Surface style={[styles.card, { backgroundColor: theme?.colors?.surface }]} elevation={1}>
        <View style={styles.topRow}>
          <View style={{ flex: 1 }}>
            <Text style={[styles.name, { color: theme?.colors?.onSurface }]}>{customer.name}</Text>
            {customer.totalOrders > 0 ? (
              <Text style={[styles.metaText, { color: theme?.colors?.onSurfaceVariant, marginTop: 2 }]}>Joined: {joinedDate}</Text>
            ) : (
              customer.email ? (
                <Text style={[styles.metaText, { color: theme?.colors?.onSurfaceVariant, marginTop: 2 }]}>{customer.email}</Text>
              ) : null
            )}
          </View>
          {customerStatus && (
            <View style={[styles.badge, { backgroundColor: customerStatus.backgroundColor }]}>
              <Text style={[styles.badgeText, { color: customerStatus.color }]}>{customerStatus.text}</Text>
            </View>
          )}
        </View>
        <View style={styles.bottomRow}>
          <View style={styles.leftColumn}>
            <Text style={[styles.phone, { color: theme?.colors?.onSurfaceVariant }]}>{customer.phone}</Text>
          </View>
          <View style={styles.rightColumn}>
            {customer.totalOrders > 0 && (
              <>
                <Text style={[styles.metaText, { color: theme?.colors?.onSurfaceVariant, fontWeight: 'bold' }]}>৳{customer.totalSpent?.toLocaleString() || 0} spent</Text>
                <Text style={[styles.metaText, { color: theme?.colors?.onSurfaceVariant }]}>{totalOrdersText}</Text>
                <Text style={[styles.activeOrders, { color: activeOrdersColor }]}>{activeOrdersText}</Text>
              </>
            )}
          </View>
        </View>
      </Surface>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  leftColumn: {
    flex: 1,
    justifyContent: 'center',
  },
  rightColumn: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginLeft: 16,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 6,
    marginLeft: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  phone: {
    fontSize: 14,
    marginBottom: 4,
  },
  metaText: {
    fontSize: 12,
  },
  activeOrders: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
});

export default CustomerCard; 