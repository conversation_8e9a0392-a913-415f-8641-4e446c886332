// @ts-nocheck
import React, { useCallback } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import PropTypes from 'prop-types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import ItemRow from './ItemRow';
import { getThemeWithFallback } from '../utils/themeUtils';

const MAX_HEIGHT = 350;
const PADDING = 8;
const PADDING_BOTTOM = 16;

const SortTab = React.memo(({ sortOptions, selectedSort, onSelectSort, theme, height = MAX_HEIGHT }) => {
  const { colors } = getThemeWithFallback(theme);
  const handleSelect = useCallback((option) => {
    if (!selectedSort || selectedSort.key !== option.key) {
      onSelectSort({ key: option.key, direction: 'asc' });
    } else {
      onSelectSort(prev => ({ key: option.key, direction: prev.direction === 'asc' ? 'desc' : 'asc' }));
    }
  }, [selectedSort, onSelectSort]);

  const renderItem = ({ item: option }) => {
    const active = selectedSort && selectedSort.key === option.key;
    return (
      <ItemRow key={option.key} active={active} onPress={() => handleSelect(option)} theme={theme}>
        <Text style={{
          flex: 1,
          textAlign: 'left',
          color: active ? colors.primary : colors.onSurfaceVariant,
          fontWeight: active ? 'bold' : 'normal',
          fontSize: 16,
        }}>
          {option.label} {active ? (selectedSort.direction === 'asc' ? '\u2191' : '\u2193') : ''}
        </Text>
        <Icon
          name={active ? 'radiobox-marked' : 'radiobox-blank'}
          size={22}
          color={active ? colors.primary : colors.onSurfaceVariant}
        />
      </ItemRow>
    );
  };

  return (
    <View style={{ maxHeight: height }}>
      <FlatList
        data={sortOptions}
        renderItem={renderItem}
        keyExtractor={item => item.key}
        contentContainerStyle={{ padding: PADDING, paddingBottom: PADDING_BOTTOM }}
        keyboardShouldPersistTaps="handled"
      />
    </View>
  );
});

SortTab.propTypes = {
  sortOptions: PropTypes.array.isRequired,
  selectedSort: PropTypes.shape({ key: PropTypes.string, direction: PropTypes.string }),
  onSelectSort: PropTypes.func.isRequired,
  theme: PropTypes.object.isRequired,
  height: PropTypes.number,
};

const styles = (theme) => StyleSheet.create({
  activeText: {
    flex: 1,
    textAlign: 'left',
    color: theme.primary,
    fontWeight: 'bold',
    fontSize: 16,
  },
  inactiveText: {
    flex: 1,
    textAlign: 'left',
    color: theme.onSurfaceVariant,
    fontWeight: 'normal',
    fontSize: 16,
  },
});

export default SortTab; 