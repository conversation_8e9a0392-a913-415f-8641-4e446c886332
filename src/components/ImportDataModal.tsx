// @ts-nocheck
import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  useTheme,
  Card,
  List,
  Divider,
  Surface,
  TextInput,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ImportDataModal = ({ visible, onDismiss, onImport }) => {
  const theme = useTheme();
  const [importMethod, setImportMethod] = useState('sample');
  const [jsonData, setJsonData] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const sampleData = {
    products: [],
    orders: [],
    customers: [],
  };

  const handleImport = async () => {
    setIsLoading(true);

    try {
      let dataToImport;

      if (importMethod === 'sample') {
        dataToImport = sampleData;
      } else if (importMethod === 'json') {
        if (!jsonData.trim()) {
          Alert.alert('Error', 'Please enter JSON data to import');
          setIsLoading(false);
          return;
        }

        try {
          dataToImport = JSON.parse(jsonData);
        } catch (error) {
          Alert.alert('Error', 'Invalid JSON format. Please check your data and try again.');
          setIsLoading(false);
          return;
        }
      }

      // Validate data structure
      if (!dataToImport.products && !dataToImport.orders && !dataToImport.customers) {
        Alert.alert('Error', 'Import data must contain at least products, orders, or customers');
        setIsLoading(false);
        return;
      }

      // Show confirmation
      const productCount = dataToImport.products?.length || 0;
      const orderCount = dataToImport.orders?.length || 0;
      const customerCount = dataToImport.customers?.length || 0;

      Alert.alert(
        'Confirm Import',
        `Import ${productCount} products, ${orderCount} orders, and ${customerCount} customers?\n\nThis will add to your existing data.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Import',
            onPress: () => {
              onImport(dataToImport);
              onDismiss();
              Alert.alert('Success', 'Data imported successfully!');
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to import data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const ImportMethodCard = ({ method, title, description, icon, selected, onSelect }) => (
    <Card
      style={[
        styles.methodCard,
        selected && { backgroundColor: theme.colors.primaryContainer }
      ]}
      mode={selected ? "contained" : "outlined"}
      onPress={onSelect}
    >
      <Card.Content>
        <View style={styles.methodHeader}>
          <Icon
            name={icon}
            size={24}
            color={selected ? theme.colors.onPrimaryContainer : theme.colors.primary}
          />
          <View style={styles.methodText}>
            <Text
              variant="titleMedium"
              style={{ color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurface }}
            >
              {title}
            </Text>
            <Text
              variant="bodySmall"
              style={{ color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurfaceVariant }}
            >
              {description}
            </Text>
          </View>
          {selected && (
            <Icon
              name="check-circle"
              size={20}
              color={theme.colors.onPrimaryContainer}
            />
          )}
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Import Data
          </Text>

          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Choose Import Method
          </Text>

          <ImportMethodCard
            method="sample"
            title="Sample Data"
            description="Import pre-made sample products, orders, and customers"
            icon="database-plus"
            selected={importMethod === 'sample'}
            onSelect={() => setImportMethod('sample')}
          />

          <ImportMethodCard
            method="json"
            title="JSON Data"
            description="Import from custom JSON data"
            icon="code-json"
            selected={importMethod === 'json'}
            onSelect={() => setImportMethod('json')}
          />

          {importMethod === 'sample' && (
            <Surface style={[styles.previewCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
              <Text variant="titleMedium" style={{ marginBottom: 12 }}>
                Sample Data Preview
              </Text>
              <View style={styles.previewStats}>
                <View style={styles.previewStat}>
                  <Text variant="headlineSmall" style={{ color: theme.colors.primary }}>
                    {sampleData.products.length}
                  </Text>
                  <Text variant="bodySmall">Products</Text>
                </View>
                <View style={styles.previewStat}>
                  <Text variant="headlineSmall" style={{ color: theme.colors.secondary }}>
                    {sampleData.orders.length}
                  </Text>
                  <Text variant="bodySmall">Orders</Text>
                </View>
                <View style={styles.previewStat}>
                  <Text variant="headlineSmall" style={{ color: theme.colors.tertiary }}>
                    {sampleData.customers.length}
                  </Text>
                  <Text variant="bodySmall">Customers</Text>
                </View>
              </View>
            </Surface>
          )}

          {importMethod === 'json' && (
            <View style={styles.jsonSection}>
              <UnifiedTextInput
                label="JSON Data"
                value={jsonData}
                onChangeText={setJsonData}
                multiline
                numberOfLines={8}
                style={styles.jsonInput}
                placeholder='{"products": [...], "orders": [...], "customers": [...]}'
              />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }}>
                Paste your JSON data here. The data should contain products, orders, and/or customers arrays.
              </Text>
            </View>
          )}

          <Surface style={[styles.infoCard, { backgroundColor: theme.colors.errorContainer }]} elevation={1}>
            <View style={styles.infoHeader}>
              <Icon name="information" size={20} color={theme.colors.onErrorContainer} />
              <Text variant="titleSmall" style={{ marginLeft: 8, color: theme.colors.onErrorContainer }}>
                Important
              </Text>
            </View>
            <Text variant="bodySmall" style={{ color: theme.colors.onErrorContainer, marginTop: 8 }}>
              Importing data will add to your existing data, not replace it. Make sure to backup your current data before importing.
            </Text>
          </Surface>

          <View style={styles.buttonRow}>
            <Button
              mode="outlined"
              onPress={onDismiss}
              style={styles.button}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleImport}
              style={styles.button}
              loading={isLoading}
              disabled={isLoading}
            >
              Import Data
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '90%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  methodCard: {
    marginBottom: 12,
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodText: {
    marginLeft: 12,
    flex: 1,
  },
  previewCard: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  previewStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  previewStat: {
    alignItems: 'center',
  },
  jsonSection: {
    marginTop: 16,
  },
  jsonInput: {
    fontFamily: 'monospace',
  },
  infoCard: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default ImportDataModal;
