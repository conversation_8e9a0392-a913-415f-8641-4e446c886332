// @ts-nocheck
import React, { useState, useMemo, useCallback, forwardRef, useRef, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { TabView } from 'react-native-tab-view';
import UnifiedBottomSheet from './UnifiedBottomSheet';
import { useTheme } from '../context/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import FilterTab from './FilterTab';
import SortTab from './SortTab';
import PropTypes from 'prop-types';
import { getThemeWithFallback } from '../utils/themeUtils';

function renderSwitchTabBar(props) {
  const { navigationState, jumpTo } = props;
  const TAB_BAR_RADIUS = 12;
  const BUTTON_RADIUS = 12;
  const BUTTON_HEIGHT = 36;
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: '#151515FF',
      borderRadius: TAB_BAR_RADIUS,
      overflow: 'hidden',
      marginHorizontal: 8, // increased from 16
      marginTop: 4, // increased from 12
      marginBottom: 4, // increased from 8
      padding: 6, // increased from 4
      width: 'auto',
      alignSelf: 'contain',
    },
    button: {
      flex: 1,
      borderRadius: BUTTON_RADIUS,
      backgroundColor: 'transparent',
      elevation: 0,
      minHeight: BUTTON_HEIGHT,
    },
    buttonActive: {
      backgroundColor: '#242526',
    },
    label: {
      color: '#D8D8D8FF',
      fontWeight: 'regular',
      fontSize: 14,
      textTransform: 'none',
    },
    labelActive: {
      color: '#FFFFFF',
    },
    leftRadius: {
      borderTopLeftRadius: TAB_BAR_RADIUS,
      borderBottomLeftRadius: TAB_BAR_RADIUS,
    },
    rightRadius: {
      borderTopRightRadius: TAB_BAR_RADIUS,
      borderBottomRightRadius: TAB_BAR_RADIUS,
    },
  });
  return (
    <View style={styles.container}>
      {navigationState.routes.map((route, i) => {
        const active = navigationState.index === i;
        const isFirst = i === 0;
        const isLast = i === navigationState.routes.length - 1;
        return (
          <Button
            key={route.key}
            mode={active ? 'contained' : 'text'}
            onPress={() => jumpTo(route.key)}
            style={[
              styles.button,
              active && styles.buttonActive,
              isFirst && styles.leftRadius,
              isLast && styles.rightRadius,
            ]}
            labelStyle={[styles.label, active && styles.labelActive]}
            contentStyle={{ height: BUTTON_HEIGHT }}
            accessibilityRole="tab"
            accessibilityState={{ selected: !!active }}
            accessibilityLabel={route.title}
          >
            {route.title}
          </Button>
        );
      })}
    </View>
  );
}

const MIN_HEIGHT = 400;
const PADDING_HORIZONTAL = 16;
const PADDING_TOP = 8;
const BORDER_WIDTH = 0;
const BUTTON_PADDING_BOTTOM = 16;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: MIN_HEIGHT,
    paddingHorizontal: 4, // increased from 0
    paddingTop: 12, // increased from 0
  },
  tabViewContainer: {
    flex: 1,
  },
  buttonContainer: {
    paddingBottom: BUTTON_PADDING_BOTTOM,
    paddingHorizontal: PADDING_HORIZONTAL,
    paddingTop: PADDING_TOP,
    borderTopWidth: BORDER_WIDTH,
  },
});

const FilterSortBottomSheet = forwardRef(({
  filters = [],
  selectedFilter,
  onSelectFilter,
  sortOptions = [],
  selectedSort,
  onSelectSort,
  onConfirm,
  title = 'Filter',
  snapPoints = ['50%'],
}, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [tabIndex, setTabIndex] = useState(0);
  const [routes] = useState([
    { key: 'filters', title: 'Filters' },
    { key: 'sorting', title: 'Sorting' },
  ]);

  // Temporary state for filter and sort
  const [tempFilter, setTempFilter] = useState(selectedFilter);
  const [tempSort, setTempSort] = useState(selectedSort);

  // Reset temp state when the sheet is opened or closed
  useEffect(() => {
    setTempFilter(selectedFilter);
    setTempSort(selectedSort);
  }, [selectedFilter, selectedSort]);

  // Memoize scenes for TabView
  const renderScene = useCallback(({ route }) => {
    switch (route.key) {
      case 'filters':
        return <FilterTab filters={filters} selectedFilter={tempFilter} onSelectFilter={setTempFilter} theme={theme} height={350} />;
      case 'sorting':
        return <SortTab sortOptions={sortOptions} selectedSort={tempSort} onSelectSort={setTempSort} theme={theme} height={350} />;
      default:
        return null;
    }
  }, [filters, tempFilter, setTempFilter, sortOptions, tempSort, setTempSort, theme]);

  // Only update parent when Show Results is clicked
  const handleShowResults = () => {
    if (onSelectFilter) onSelectFilter(tempFilter);
    if (onSelectSort) onSelectSort(tempSort);
    if (onConfirm) onConfirm();
  };

  return (
    <UnifiedBottomSheet
      ref={ref}
      title={title}
      snapPoints={snapPoints}
      onClose={() => {
        setTempFilter(selectedFilter);
        setTempSort(selectedSort);
      }}
    >
      <View style={styles.container}>
        <View style={styles.tabViewContainer}>
          <TabView
            navigationState={{ index: tabIndex, routes }}
            renderScene={renderScene}
            onIndexChange={setTabIndex}
            renderTabBar={props => renderSwitchTabBar(props)}
            lazy
            renderLazyPlaceholder={() => null}
            animationEnabled={false}
            sceneContainerStyle={{ flex: 1 }}
          />
        </View>
        <View style={[styles.buttonContainer, { borderColor: (getThemeWithFallback(theme)?.colors?.outline || '#eee') + '20', paddingBottom: insets.bottom + BUTTON_PADDING_BOTTOM }]}>
          <Button
            mode="contained"
            onPress={handleShowResults}
            accessibilityRole="button"
            accessibilityLabel="Show Results"
          >
            Show Results
          </Button>
        </View>
      </View>
    </UnifiedBottomSheet>
  );
});

FilterSortBottomSheet.propTypes = {
  filters: PropTypes.array,
  selectedFilter: PropTypes.string,
  onSelectFilter: PropTypes.func,
  sortOptions: PropTypes.array,
  selectedSort: PropTypes.shape({ key: PropTypes.string, direction: PropTypes.string }),
  onSelectSort: PropTypes.func,
  onConfirm: PropTypes.func,
  title: PropTypes.string,
  snapPoints: PropTypes.array,
};

export default FilterSortBottomSheet; 