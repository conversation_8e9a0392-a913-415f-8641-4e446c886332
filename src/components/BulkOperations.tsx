import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Text, Checkbox, Button, Surface, useTheme, Chip, IconButton } from 'react-native-paper';
import { useData } from '../context/DataContext';
import LoggingService from '../services/LoggingService';

export interface BulkOperationItem {
  id: string;
  title: string;
  subtitle?: string;
  data: any;
}

export interface BulkOperationConfig {
  type: 'products' | 'customers' | 'orders';
  items: BulkOperationItem[];
  onSelectionChange?: (selectedIds: string[]) => void;
  onBulkAction?: (action: string, selectedIds: string[]) => void;
}

interface BulkOperationsProps {
  config: BulkOperationConfig;
}

const BulkOperations: React.FC<BulkOperationsProps> = ({ config }) => {
  const theme = useTheme();
  const { actions } = useData();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const isAllSelected = useMemo(() => {
    return config.items.length > 0 && selectedIds.length === config.items.length;
  }, [selectedIds.length, config.items.length]);

  const isPartiallySelected = useMemo(() => {
    return selectedIds.length > 0 && selectedIds.length < config.items.length;
  }, [selectedIds.length, config.items.length]);

  const handleSelectAll = useCallback(() => {
    const newSelection = isAllSelected ? [] : config.items.map(item => item.id);
    setSelectedIds(newSelection);
    config.onSelectionChange?.(newSelection);

    LoggingService.debug('Bulk selection changed', 'BULK_OPS', {
      type: config.type,
      action: isAllSelected ? 'deselect_all' : 'select_all',
      count: newSelection.length,
    });
  }, [isAllSelected, config]);

  const handleItemSelect = useCallback((itemId: string) => {
    const newSelection = selectedIds.includes(itemId)
      ? selectedIds.filter(id => id !== itemId)
      : [...selectedIds, itemId];

    setSelectedIds(newSelection);
    config.onSelectionChange?.(newSelection);

    LoggingService.debug('Item selection changed', 'BULK_OPS', {
      type: config.type,
      itemId,
      selected: !selectedIds.includes(itemId),
      totalSelected: newSelection.length,
    });
  }, [selectedIds, config]);

  const handleBulkDelete = useCallback(async () => {
    if (selectedIds.length === 0) return;

    Alert.alert(
      'Confirm Bulk Delete',
      `Are you sure you want to delete ${selectedIds.length} ${config.type}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              LoggingService.info('Starting bulk delete operation', 'BULK_OPS', {
                type: config.type,
                count: selectedIds.length,
                ids: selectedIds,
              });

              // Perform bulk delete based on type
              for (const id of selectedIds) {
                switch (config.type) {
                  case 'products':
                    await actions.deleteProduct(id);
                    break;
                  case 'customers':
                    await actions.deleteCustomer(id);
                    break;
                  case 'orders':
                    await actions.deleteOrder(id);
                    break;
                }
              }

              setSelectedIds([]);
              config.onSelectionChange?.([]);
              config.onBulkAction?.('delete', selectedIds);

              LoggingService.info('Bulk delete completed successfully', 'BULK_OPS', {
                type: config.type,
                deletedCount: selectedIds.length,
              });

              Alert.alert('Success', `Successfully deleted ${selectedIds.length} ${config.type}.`);
            } catch (error) {
              LoggingService.error('Bulk delete failed', 'BULK_OPS', error instanceof Error ? error : new Error(String(error)));
              Alert.alert('Error', 'Failed to delete some items. Please try again.');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  }, [selectedIds, config, actions]);

  const handleBulkExport = useCallback(async () => {
    if (selectedIds.length === 0) return;

    setIsProcessing(true);
    try {
      LoggingService.info('Starting bulk export operation', 'BULK_OPS', {
        type: config.type,
        count: selectedIds.length,
      });

      const selectedItems = config.items.filter(item => selectedIds.includes(item.id));
      const exportData = selectedItems.map(item => item.data);

      // Create CSV content
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(item =>
          headers.map(header => `"${item[header] || ''}"`).join(',')
        )
      ].join('\n');

      // In a real app, you would use a file system library to save the CSV
      console.log('Export data:', csvContent);

      config.onBulkAction?.('export', selectedIds);

      LoggingService.info('Bulk export completed', 'BULK_OPS', {
        type: config.type,
        exportedCount: selectedIds.length,
      });

      Alert.alert('Success', `Successfully exported ${selectedIds.length} ${config.type}.`);
    } catch (error) {
      LoggingService.error('Bulk export failed', 'BULK_OPS', error instanceof Error ? error : new Error(String(error)));
      Alert.alert('Error', 'Failed to export data. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [selectedIds, config]);

  const handleBulkStatusUpdate = useCallback(async (status: string) => {
    if (selectedIds.length === 0) return;

    setIsProcessing(true);
    try {
      LoggingService.info('Starting bulk status update', 'BULK_OPS', {
        type: config.type,
        status,
        count: selectedIds.length,
      });

      // Update status based on type
      for (const id of selectedIds) {
        const item = config.items.find(item => item.id === id);
        if (item) {
          const updatedData = { ...item.data, status };

          switch (config.type) {
            case 'products':
              await actions.updateProduct({ ...updatedData, id });
              break;
            case 'customers':
              await actions.updateCustomer({ ...updatedData, id });
              break;
            case 'orders':
              await actions.updateOrder({ ...updatedData, id });
              break;
          }
        }
      }

      setSelectedIds([]);
      config.onSelectionChange?.([]);
      config.onBulkAction?.('status_update', selectedIds);

      LoggingService.info('Bulk status update completed', 'BULK_OPS', {
        type: config.type,
        status,
        updatedCount: selectedIds.length,
      });

      Alert.alert('Success', `Successfully updated ${selectedIds.length} ${config.type} to ${status}.`);
    } catch (error) {
      LoggingService.error('Bulk status update failed', 'BULK_OPS', error instanceof Error ? error : new Error(String(error)));
      Alert.alert('Error', 'Failed to update status. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [selectedIds, config, actions]);

  const renderSelectionHeader = () => (
    <Surface style={[styles.selectionHeader, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
      <View style={styles.selectionRow}>
        <Checkbox
          status={isAllSelected ? 'checked' : isPartiallySelected ? 'indeterminate' : 'unchecked'}
          onPress={handleSelectAll}
        />
        <Text variant="titleMedium" style={[styles.selectionText, { color: theme.colors.onSurface }]}>
          {selectedIds.length > 0
            ? `${selectedIds.length} of ${config.items.length} selected`
            : `Select all ${config.items.length} items`
          }
        </Text>
        {selectedIds.length > 0 && (
          <IconButton
            icon="close"
            size={20}
            onPress={() => {
              setSelectedIds([]);
              config.onSelectionChange?.([]);
            }}
          />
        )}
      </View>
    </Surface>
  );

  const renderBulkActions = () => {
    if (selectedIds.length === 0) return null;

    const getStatusOptions = () => {
      switch (config.type) {
        case 'orders':
          return ['pending', 'processing', 'completed', 'cancelled'];
        case 'products':
          return ['active', 'inactive', 'discontinued'];
        case 'customers':
          return ['active', 'inactive'];
        default:
          return [];
      }
    };

    return (
      <Surface style={[styles.bulkActions, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <Text variant="titleSmall" style={[styles.actionsTitle, { color: theme.colors.onSurface }]}>
          Bulk Actions ({selectedIds.length} selected)
        </Text>

        <View style={styles.actionButtons}>
          <Button
            mode="outlined"
            onPress={handleBulkExport}
            disabled={isProcessing}
            icon="download"
            style={styles.actionButton}
          >
            Export
          </Button>

          <Button
            mode="contained"
            onPress={handleBulkDelete}
            disabled={isProcessing}
            buttonColor={theme.colors.error}
            icon="delete"
            style={styles.actionButton}
          >
            Delete
          </Button>
        </View>

        {getStatusOptions().length > 0 && (
          <View style={styles.statusActions}>
            <Text variant="bodySmall" style={[styles.statusLabel, { color: theme.colors.onSurfaceVariant }]}>
              Update Status:
            </Text>
            <View style={styles.statusChips}>
              {getStatusOptions().map(status => (
                <Chip
                  key={status}
                  mode="outlined"
                  onPress={() => handleBulkStatusUpdate(status)}
                  disabled={isProcessing}
                  style={styles.statusChip}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Chip>
              ))}
            </View>
          </View>
        )}
      </Surface>
    );
  };

  const renderItems = () => (
    <View style={styles.itemsList}>
      {config.items.map(item => (
        <Surface
          key={item.id}
          style={[
            styles.itemRow,
            { backgroundColor: theme.colors.surface },
            selectedIds.includes(item.id) && { backgroundColor: theme.colors.primaryContainer }
          ]}
          elevation={0}
        >
          <Checkbox
            status={selectedIds.includes(item.id) ? 'checked' : 'unchecked'}
            onPress={() => handleItemSelect(item.id)}
          />
          <View style={styles.itemContent}>
            <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>
              {item.title}
            </Text>
            {item.subtitle && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {item.subtitle}
              </Text>
            )}
          </View>
        </Surface>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {renderSelectionHeader()}
      {renderBulkActions()}
      {renderItems()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  selectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
    borderRadius: 8,
  },
  selectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionText: {
    flex: 1,
    marginLeft: 8,
  },
  bulkActions: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  actionsTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
  },
  statusActions: {
    marginTop: 8,
  },
  statusLabel: {
    marginBottom: 8,
    fontWeight: '500',
  },
  statusChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusChip: {
    marginRight: 0,
  },
  itemsList: {
    gap: 4,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  itemContent: {
    flex: 1,
    marginLeft: 12,
  },
});

export default BulkOperations;

// BottomSheet Manager for consistent bottom sheet handling
export class BottomSheetManager {
  private static instance: BottomSheetManager;
  private bottomSheetRef: React.RefObject<any> | null = null;

  static getInstance(): BottomSheetManager {
    if (!BottomSheetManager.instance) {
      BottomSheetManager.instance = new BottomSheetManager();
    }
    return BottomSheetManager.instance;
  }

  setBottomSheetRef(ref: React.RefObject<any>) {
    this.bottomSheetRef = ref;
  }

  present(config: any) {
    this.bottomSheetRef?.current?.present(config);
  }

  dismiss() {
    this.bottomSheetRef?.current?.dismiss();
  }

  // Product operations
  showProductDetails(product: any) {
    this.present({
      title: product.name,
      subtitle: `SKU: ${product.sku} • ৳${product.price}`,
      content: this.renderProductDetails(product),
      actions: [
        {
          id: 'edit',
          label: 'Edit Product',
          variant: 'primary' as const,
          onPress: () => {
            this.dismiss();
            // Navigate to edit screen
          }
        },
        {
          id: 'delete',
          label: 'Delete',
          variant: 'danger' as const,
          onPress: () => {
            this.dismiss();
            // Handle delete
          }
        }
      ]
    });
  }

  // Order operations
  showOrderDetails(order: any) {
    this.present({
      title: `Order #${order.id}`,
      subtitle: `${order.customerName} • ৳${order.total}`,
      content: this.renderOrderDetails(order),
      actions: [
        {
          id: 'edit',
          label: 'Edit Order',
          variant: 'primary' as const,
          onPress: () => {
            this.dismiss();
            // Navigate to edit screen
          }
        },
        {
          id: 'complete',
          label: 'Mark Complete',
          variant: 'secondary' as const,
          onPress: () => {
            this.dismiss();
            // Handle status update
          }
        }
      ]
    });
  }

  // Customer operations
  showCustomerDetails(customer: any) {
    this.present({
      title: customer.name,
      subtitle: `${customer.email} • ${customer.phone}`,
      content: this.renderCustomerDetails(customer),
      actions: [
        {
          id: 'edit',
          label: 'Edit Customer',
          variant: 'primary' as const,
          onPress: () => {
            this.dismiss();
            // Navigate to edit screen
          }
        },
        {
          id: 'orders',
          label: 'View Orders',
          variant: 'secondary' as const,
          onPress: () => {
            this.dismiss();
            // Navigate to customer orders
          }
        }
      ]
    });
  }

  // Bulk operations
  showBulkOperations(config: any) {
    this.present({
      title: `Bulk Operations`,
      subtitle: `Manage multiple ${config.type}`,
      content: React.createElement(BulkOperations, { config }),
      enableDynamicSizing: true,
      maxHeight: 600,
    });
  }

  private renderProductDetails(product: any) {
    const { ProductDetails } = require('./BottomSheetContent');
    return React.createElement(ProductDetails, { product });
  }

  private renderOrderDetails(order: any) {
    const { OrderDetails } = require('./BottomSheetContent');
    return React.createElement(OrderDetails, { order });
  }

  private renderCustomerDetails(customer: any) {
    const { CustomerDetails } = require('./BottomSheetContent');
    return React.createElement(CustomerDetails, { customer });
  }
}
