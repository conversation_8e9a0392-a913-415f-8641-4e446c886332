/**
 * StandardIcon - Consistent icon component using standardized mappings
 * Ensures all icons across the app follow the same conventions
 */

import React from 'react';
import { ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { getStandardIcon, isStandardIcon } from '../constants/iconMappings';

type IconCategory = 'action' | 'status' | 'business' | 'navigation' | 'ui';

interface StandardIconProps {
  // Standard props
  category?: IconCategory;
  name?: string; // The semantic name (e.g., 'edit', 'pending', 'product')

  // Direct icon override
  icon?: string; // Direct MaterialCommunityIcons name (bypasses standardization)

  // Visual props
  size?: number;
  color?: string;

  // Style props
  style?: ViewStyle;

  // Accessibility
  accessibilityLabel?: string;

  // Debug mode
  debug?: boolean;
}

const StandardIcon: React.FC<StandardIconProps> = ({
  // Standard props
  category,
  name,

  // Direct icon override
  icon,

  // Visual props
  size = 24,
  color,

  // Style props
  style,

  // Accessibility
  accessibilityLabel,

  // Debug mode
  debug = false,
}) => {
  // Determine the actual icon to use
  let finalIcon;
  
  if (icon) {
    // Direct icon specified - use as is
    finalIcon = icon;
  } else if (category && name) {
    // Use standardized mapping
    finalIcon = getStandardIcon(category, name);
  } else if (name) {
    // Try to find icon by name across all categories
    finalIcon = getStandardIcon('ui' as IconCategory, name);
  } else {
    // Fallback
    finalIcon = 'help-circle-outline';
  }
  
  // Debug logging
  if (debug || __DEV__) {
    if (!isStandardIcon(finalIcon) && !icon) {
      console.warn(`StandardIcon: Non-standard icon "${finalIcon}" used for ${category}:${name}`);
    }
  }
  
  return (
    <Icon
      name={finalIcon}
      size={size}
      color={color}
      style={style}
      accessibilityLabel={accessibilityLabel || `${category} ${name} icon`}
    />
  );
};

// Preset components for common use cases
export const ActionIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => (
  <StandardIcon category="action" name={name} {...props} />
);

export const StatusIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => (
  <StandardIcon category="status" name={name} {...props} />
);

export const BusinessIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => (
  <StandardIcon category="business" name={name} {...props} />
);

export const NavigationIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => (
  <StandardIcon category="navigation" name={name} {...props} />
);

export const UIIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => (
  <StandardIcon category="ui" name={name} {...props} />
);

// Common action icons as components
export const EditIcon: React.FC<any> = (props) => <ActionIcon name="edit" {...props} />;
export const DeleteIcon: React.FC<any> = (props) => <ActionIcon name="delete" {...props} />;
export const AddIcon: React.FC<any> = (props) => <ActionIcon name="add" {...props} />;
export const SaveIcon: React.FC<any> = (props) => <ActionIcon name="save" {...props} />;
export const CancelIcon: React.FC<any> = (props) => <ActionIcon name="cancel" {...props} />;
export const SearchIcon: React.FC<any> = (props) => <ActionIcon name="search" {...props} />;
export const FilterIcon: React.FC<any> = (props) => <ActionIcon name="filter" {...props} />;
export const MenuIcon: React.FC<any> = (props) => <ActionIcon name="menu" {...props} />;
export const SettingsIcon: React.FC<any> = (props) => <ActionIcon name="settings" {...props} />;
export const RefreshIcon: React.FC<any> = (props) => <ActionIcon name="refresh" {...props} />;

// Common status icons as components
export const PendingIcon: React.FC<any> = (props) => <StatusIcon name="pending" {...props} />;
export const ProcessingIcon: React.FC<any> = (props) => <StatusIcon name="processing" {...props} />;
export const CompletedIcon: React.FC<any> = (props) => <StatusIcon name="completed" {...props} />;
export const CancelledIcon: React.FC<any> = (props) => <StatusIcon name="cancelled" {...props} />;
export const SuccessIcon: React.FC<any> = (props) => <StatusIcon name="success" {...props} />;
export const ErrorIcon: React.FC<any> = (props) => <StatusIcon name="error" {...props} />;
export const WarningIcon: React.FC<any> = (props) => <StatusIcon name="warning" {...props} />;
export const InfoIcon: React.FC<any> = (props) => <StatusIcon name="info" {...props} />;

// Common business icons as components
export const ProductIcon: React.FC<any> = (props) => <BusinessIcon name="product" {...props} />;
export const OrderIcon: React.FC<any> = (props) => <BusinessIcon name="order" {...props} />;
export const CustomerIcon: React.FC<any> = (props) => <BusinessIcon name="customer" {...props} />;
export const SalesIcon: React.FC<any> = (props) => <BusinessIcon name="sales" {...props} />;
export const RevenueIcon: React.FC<any> = (props) => <BusinessIcon name="revenue" {...props} />;
export const AnalyticsIcon: React.FC<any> = (props) => <BusinessIcon name="analytics" {...props} />;
export const ReportsIcon: React.FC<any> = (props) => <BusinessIcon name="reports" {...props} />;
export const DashboardIcon: React.FC<any> = (props) => <BusinessIcon name="dashboard" {...props} />;

// Common navigation icons as components
export const DashboardNavIcon: React.FC<any> = (props) => <NavigationIcon name="dashboard" {...props} />;
export const ScanNavIcon: React.FC<any> = (props) => <NavigationIcon name="scan" {...props} />;
export const PlusNavIcon: React.FC<any> = (props) => <NavigationIcon name="plus" {...props} />;
export const OrdersNavIcon: React.FC<any> = (props) => <NavigationIcon name="orders" {...props} />;
export const SettingsNavIcon: React.FC<any> = (props) => <NavigationIcon name="settings" {...props} />;

// Icon with automatic color based on status
export const StatusIconWithColor: React.FC<{ status: string; size?: number; [key: string]: any }> = ({ status, size = 20, ...props }) => {
  const statusColors = {
    pending: '#FF9800',
    processing: '#2196F3',
    ready: '#4CAF50',
    completed: '#8BC34A',
    cancelled: '#F44336',
    success: '#4CAF50',
    error: '#F44336',
    warning: '#FF9800',
    info: '#2196F3',
    active: '#4CAF50',
    inactive: '#9E9E9E',
  };
  
  return (
    <StatusIcon
      name={status}
      size={size}
      color={(statusColors as any)[status] || '#9E9E9E'}
      {...props}
    />
  );
};

// Icon with automatic color based on action
export const ActionIconWithColor: React.FC<{ action: string; theme: any; size?: number; [key: string]: any }> = ({ action, theme, size = 20, ...props }) => {
  const getActionColor = (actionName: string, theme: any): string => {
    const actionColors = {
      edit: theme?.colors?.primary || '#2196F3',
      delete: theme?.colors?.error || '#F44336',
      add: theme?.colors?.primary || '#4CAF50',
      save: theme?.colors?.primary || '#4CAF50',
      cancel: theme?.colors?.onSurfaceVariant || '#9E9E9E',
      search: theme?.colors?.onSurface || '#424242',
      filter: theme?.colors?.onSurface || '#424242',
      menu: theme?.colors?.onSurfaceVariant || '#9E9E9E',
      settings: theme?.colors?.onSurfaceVariant || '#9E9E9E',
    };
    
    return (actionColors as any)[actionName] || theme?.colors?.onSurface || '#424242';
  };
  
  return (
    <ActionIcon
      name={action}
      size={size}
      color={getActionColor(action, theme)}
      {...props}
    />
  );
};

export default StandardIcon;
