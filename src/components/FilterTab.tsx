// @ts-nocheck
import React, { useCallback } from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import PropTypes from 'prop-types';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import ItemRow from './ItemRow';
import { getThemeWithFallback } from '../utils/themeUtils';

const MAX_HEIGHT = 350;
const PADDING = 8;
const PADDING_BOTTOM = 16;

const FilterTab = React.memo(({ filters, selectedFilter, onSelectFilter, theme, height = MAX_HEIGHT }) => {
  const themeWithFallback = getThemeWithFallback(theme);
  const handleSelect = useCallback((filter) => {
    if (selectedFilter !== filter) onSelectFilter(filter);
  }, [selectedFilter, onSelectFilter]);

  const renderItem = ({ item: filter }) => {
    const active = selectedFilter === filter;
    return (
      <ItemRow key={filter} active={active} onPress={() => handleSelect(filter)} theme={themeWithFallback}>
        <Text style={active ? styles(themeWithFallback).activeText : styles(themeWithFallback).inactiveText}>{filter}</Text>
        <Icon
          name={active ? 'radiobox-marked' : 'radiobox-blank'}
          size={22}
          color={active ? themeWithFallback.colors.primary : themeWithFallback.colors.onSurfaceVariant}
        />
      </ItemRow>
    );
  };

  return (
    <View style={{ maxHeight: height }}>
      <FlatList
        data={filters}
        renderItem={renderItem}
        keyExtractor={item => item}
        contentContainerStyle={{ padding: PADDING, paddingBottom: PADDING_BOTTOM }}
        keyboardShouldPersistTaps="handled"
      />
    </View>
  );
});

FilterTab.propTypes = {
  filters: PropTypes.array.isRequired,
  selectedFilter: PropTypes.string,
  onSelectFilter: PropTypes.func.isRequired,
  theme: PropTypes.object.isRequired,
  height: PropTypes.number,
};

const styles = (theme) => StyleSheet.create({
  activeText: {
    flex: 1,
    textAlign: 'left',
    color: theme.colors.primary,
    fontWeight: 'bold',
    fontSize: 16,
  },
  inactiveText: {
    flex: 1,
    textAlign: 'left',
    color: theme.colors.onSurfaceVariant,
    fontWeight: 'normal',
    fontSize: 16,
  },
});

export default FilterTab; 