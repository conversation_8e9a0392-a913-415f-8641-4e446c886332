import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text, Surface, Chip, Divider, useTheme } from 'react-native-paper';

interface ProductDetailsProps {
  product: {
    id: string;
    name: string;
    sku: string;
    price: number;
    category: string;
    description?: string;
    image?: string;
    stock: number;
    status: string;
    createdAt: string;
  };
}

export const ProductDetails: React.FC<ProductDetailsProps> = ({ product }) => {
  const theme = useTheme();

  return (
    <View style={styles.container}>
      {product.image && (
        <Surface style={[styles.imageContainer, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
          <Image source={{ uri: product.image }} style={styles.productImage} resizeMode="cover" />
        </Surface>
      )}
      
      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Product Information
        </Text>
        <View style={styles.infoGrid}>
          <InfoItem label="SKU" value={product.sku} />
          <InfoItem label="Category" value={product.category} />
          <InfoItem label="Stock" value={`${product.stock} units`} />
          <InfoItem label="Created" value={new Date(product.createdAt).toLocaleDateString()} />
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Status & Pricing
        </Text>
        <View style={styles.statusRow}>
          <Chip
            mode="outlined"
            style={[
              styles.statusChip,
              { backgroundColor: product.status === 'active' ? theme.colors.primaryContainer : theme.colors.errorContainer }
            ]}
          >
            {(product.status ?? 'UNKNOWN').toUpperCase()}
          </Chip>
          <Text variant="headlineSmall" style={[styles.price, { color: theme.colors.primary }]}>
            ৳{product.price.toFixed(2)}
          </Text>
        </View>
      </View>

      {product.description && (
        <>
          <Divider style={styles.divider} />
          <View style={styles.section}>
            <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Description
            </Text>
            <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
              {product.description}
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

interface OrderDetailsProps {
  order: {
    id: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    items: Array<{
      productName: string;
      quantity: number;
      price: number;
      total: number;
    }>;
    subtotal: number;
    tax: number;
    total: number;
    status: string;
    createdAt: string;
    notes?: string;
  };
}

export const OrderDetails: React.FC<OrderDetailsProps> = ({ order }) => {
  const theme = useTheme();

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Customer Information
        </Text>
        <View style={styles.customerInfo}>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
            {order.customerName}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            {order.customerEmail}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            {order.customerPhone}
          </Text>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Order Items
        </Text>
        {order.items.map((item, index) => (
          <Surface key={index} style={[styles.orderItem, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.itemHeader}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, flex: 1 }}>
                {item.productName}
              </Text>
              <Text variant="bodyLarge" style={[styles.itemTotal, { color: theme.colors.primary }]}>
                ৳{item.total.toFixed(2)}
              </Text>
            </View>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {item.quantity} × ৳{item.price.toFixed(2)}
            </Text>
          </Surface>
        ))}
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Order Summary
        </Text>
        <View style={styles.summaryRow}>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Subtotal</Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>৳{order.subtotal.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Tax</Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>৳{order.tax.toFixed(2)}</Text>
        </View>
        <Divider style={[styles.divider, { marginVertical: 8 }]} />
        <View style={styles.summaryRow}>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>Total</Text>
          <Text variant="titleMedium" style={[styles.totalAmount, { color: theme.colors.primary }]}>
            ৳{order.total.toFixed(2)}
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.statusRow}>
          <Chip
            mode="outlined"
            style={[
              styles.statusChip,
              { backgroundColor: getOrderStatusColor(order.status, theme) }
            ]}
          >
            {order.status.toUpperCase()}
          </Chip>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {new Date(order.createdAt).toLocaleDateString()}
          </Text>
        </View>
      </View>

      {order.notes && (
        <>
          <Divider style={styles.divider} />
          <View style={styles.section}>
            <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Notes
            </Text>
            <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
              {order.notes}
            </Text>
          </View>
        </>
      )}
    </View>
  );
};

interface CustomerDetailsProps {
  customer: {
    id: string;
    name: string;
    email: string;
    phone: string;
    address?: string;
    totalOrders: number;
    totalSpent: number;
    status: string;
    createdAt: string;
    lastOrderDate?: string;
  };
}

export const CustomerDetails: React.FC<CustomerDetailsProps> = ({ customer }) => {
  const theme = useTheme();

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Contact Information
        </Text>
        <View style={styles.customerInfo}>
          <InfoItem label="Email" value={customer.email} />
          <InfoItem label="Phone" value={customer.phone} />
          {customer.address && <InfoItem label="Address" value={customer.address} />}
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Order Statistics
        </Text>
        <View style={styles.statsGrid}>
          <Surface style={[styles.statCard, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
            <Text variant="headlineSmall" style={[styles.statValue, { color: theme.colors.onPrimaryContainer }]}>
              {customer.totalOrders}
            </Text>
            <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onPrimaryContainer }]}>
              Total Orders
            </Text>
          </Surface>
          <Surface style={[styles.statCard, { backgroundColor: theme.colors.secondaryContainer }]} elevation={1}>
            <Text variant="headlineSmall" style={[styles.statValue, { color: theme.colors.onSecondaryContainer }]}>
              ৳{customer.totalSpent.toFixed(0)}
            </Text>
            <Text variant="bodySmall" style={[styles.statLabel, { color: theme.colors.onSecondaryContainer }]}>
              Total Spent
            </Text>
          </Surface>
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.section}>
        <Text variant="bodyLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Account Status
        </Text>
        <View style={styles.statusRow}>
          <Chip
            mode="outlined"
            style={[
              styles.statusChip,
              { backgroundColor: customer.status === 'active' ? theme.colors.primaryContainer : theme.colors.errorContainer }
            ]}
          >
            {customer.status.toUpperCase()}
          </Chip>
          <View style={styles.dateInfo}>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Member since {new Date(customer.createdAt).toLocaleDateString()}
            </Text>
            {customer.lastOrderDate && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Last order: {new Date(customer.lastOrderDate).toLocaleDateString()}
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const InfoItem: React.FC<{ label: string; value: string }> = ({ label, value }) => {
  const theme = useTheme();
  return (
    <View style={styles.infoItem}>
      <Text variant="bodySmall" style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
        {label}
      </Text>
      <Text variant="bodyMedium" style={[styles.infoValue, { color: theme.colors.onSurface }]}>
        {value}
      </Text>
    </View>
  );
};

const getOrderStatusColor = (status: string, theme: any) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return theme.colors.primaryContainer;
    case 'processing':
      return theme.colors.secondaryContainer;
    case 'pending':
      return theme.colors.tertiaryContainer;
    case 'cancelled':
      return theme.colors.errorContainer;
    default:
      return theme.colors.surfaceVariant;
  }
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12,
  },
  imageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    height: 200,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  infoGrid: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  infoLabel: {
    fontWeight: '500',
  },
  infoValue: {
    flex: 1,
    textAlign: 'right',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  price: {
    fontWeight: '700',
  },
  description: {
    lineHeight: 20,
  },
  divider: {
    marginVertical: 16,
  },
  customerInfo: {
    gap: 8,
  },
  orderItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemTotal: {
    fontWeight: '600',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  totalAmount: {
    fontWeight: '700',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    textAlign: 'center',
    fontWeight: '500',
  },
  dateInfo: {
    alignItems: 'flex-end',
  },
});

export default { ProductDetails, OrderDetails, CustomerDetails };
