// @ts-nocheck
/**
 * ThemeSelector - Component for selecting theme style and mode
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Text, Surface, Switch, Divider } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme, THEME_STYLES } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../theme/designTokens';

const ThemeSelector = () => {
  const { isDarkMode, themeStyle, toggleTheme, changeThemeStyle, availableThemes, theme } = useTheme();

  const handleThemeStyleChange = (newStyle) => {
    changeThemeStyle(newStyle);
  };

  const renderThemeOption = (style, label, icon, description) => {
    const isSelected = themeStyle === style;
    
    return (
      <TouchableOpacity
        style={[
          styles.themeOption,
          isSelected && { 
            borderColor: theme.colors.primary,
            backgroundColor: theme.colors.primaryContainer + '40'
          }
        ]}
        onPress={() => handleThemeStyleChange(style)}
      >
        <View style={styles.themeOptionContent}>
          <View style={[
            styles.themeIconContainer,
            { backgroundColor: isSelected ? theme.colors.primary + '20' : theme.colors.surfaceVariant }
          ]}>
            <Icon name={icon} size={24} color={isSelected ? theme.colors.primary : theme.colors.onSurfaceVariant} />
          </View>
          
          <View style={styles.themeTextContainer}>
            <Text style={{ 
              fontWeight: isSelected ? '600' : '400',
              color: theme.colors.onSurface
            }}>
              {label}
            </Text>
            <Text style={{ 
              fontSize: 12,
              color: theme.colors.onSurfaceVariant
            }}>
              {description}
            </Text>
          </View>
          
          {isSelected && (
            <Icon name="check-circle" size={20} color={theme.colors.primary} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.onSurface }]}>Appearance</Text>
      </View>
      
      <Divider style={{ marginVertical: SPACING.md }} />
      
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Theme Style</Text>
        
        {renderThemeOption(
          availableThemes.DEFAULT,
          'Default Theme',
          'palette',
          'Original bakery management theme'
        )}
        
        {renderThemeOption(
          availableThemes.INSTAGRAM,
          'Instagram Theme',
          'instagram',
          'Clean, modern Instagram-inspired design'
        )}
      </View>
      
      <Divider style={{ marginVertical: SPACING.md }} />
      
      <View style={styles.section}>
        <View style={styles.darkModeRow}>
          <View>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>Dark Mode</Text>
            <Text style={{ fontSize: 12, color: theme.colors.onSurfaceVariant }}>
              {isDarkMode ? 'Dark appearance enabled' : 'Light appearance enabled'}
            </Text>
          </View>
          
          <Switch
            value={isDarkMode}
            onValueChange={toggleTheme}
            color={theme.colors.primary}
          />
        </View>
      </View>
      
      <View style={styles.previewSection}>
        <Text style={[styles.previewText, { color: theme.colors.onSurfaceVariant }]}>
          Current theme: {themeStyle === availableThemes.INSTAGRAM ? 'Instagram' : 'Default'} ({isDarkMode ? 'Dark' : 'Light'})
        </Text>
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
  },
  header: {
    marginBottom: SPACING.sm,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: SPACING.md,
  },
  themeOption: {
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
    borderColor: 'transparent',
  },
  themeOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.md,
  },
  themeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  themeTextContainer: {
    flex: 1,
  },
  darkModeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewSection: {
    marginTop: SPACING.lg,
    alignItems: 'center',
  },
  previewText: {
    fontSize: 12,
  },
});

export default ThemeSelector;