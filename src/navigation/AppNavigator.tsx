import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme } from '../context/ThemeContext';

// Import navigators and screens
import TabNavigator from './TabNavigator';
import ProductsScreen from '../screens/ProductsScreen';
import CustomersScreen from '../screens/CustomersScreen';
import CustomerDetailsScreen from '../screens/CustomerDetailsScreen';
import SearchScreen from '../screens/SearchScreen';
import MyProfileScreen from '../screens/ProfileScreen';
import AddProductScreen from '../screens/AddProductScreen';
import AddOrderScreen from '../screens/AddOrderScreen';
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';
import SecuritySettingsScreen from '../screens/SecuritySettingsScreen';
import ImportDataScreen from '../screens/ImportDataScreen';
import ActivityLogScreen from '../screens/ActivityLogScreen';
import ReportsScreen from '../screens/ReportsScreen';
import HelpFAQScreen from '../screens/HelpFAQScreen';
import ContactSupportScreen from '../screens/ContactSupportScreen';
import AboutScreen from '../screens/AboutScreen';
import NotificationsScreen from '../screens/NotificationsScreen';

const Stack = createStackNavigator();

const AppNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // Unified transition animations
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
      initialRouteName="Main"
    >
      {/* Main Tab Navigator */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />

      {/* Modal Screens */}
      <Stack.Screen
        name="Products"
        component={ProductsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Customers Screen */}
      <Stack.Screen
        name="Customers"
        component={CustomersScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Customer Details Screen */}
      <Stack.Screen
        name="CustomerDetails"
        component={CustomerDetailsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Search Screen */}
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* My Profile Screen */}
      <Stack.Screen
        name="MyProfile"
        component={MyProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Product Screen */}
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Order Screen */}
      <Stack.Screen
        name="AddOrder"
        component={AddOrderScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Payment Methods Screen */}
      <Stack.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Security Settings Screen */}
      <Stack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Import Data Screen */}
      <Stack.Screen
        name="ImportData"
        component={ImportDataScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Activity Log Screen */}
      <Stack.Screen
        name="ActivityLog"
        component={ActivityLogScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Reports Screen */}
      <Stack.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Help & FAQ Screen */}
      <Stack.Screen
        name="HelpFAQ"
        component={HelpFAQScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Contact Support Screen */}
      <Stack.Screen
        name="ContactSupport"
        component={ContactSupportScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* About Screen */}
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Notifications Screen */}
      <Stack.Screen
        name="NotificationsScreen"
        component={NotificationsScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;