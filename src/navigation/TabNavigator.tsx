import React, { useRef, useState, useCallback, useEffect } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../context/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LoggingService from '../services/LoggingService';

// Import screens
import DashboardScreen from '../screens/DashboardScreen';
import ScanScreen from '../screens/ScanScreen';
import OrdersScreen from '../screens/OrdersScreen';
import ProductsScreen from '../screens/ProductsScreen';
import FinancialScreen from '../screens/FinancialScreen';
import ProfileScreen from '../screens/ProfileScreen';
import CustomersScreen from '../screens/CustomersScreen';

// Import custom bottom nav bar and quick actions
import BottomNavBar from '../components/BottomNavBar';
import QuickActionsBottomSheet from '../components/QuickActionsBottomSheet';
import AddCustomerBottomSheet from '../components/AddCustomerBottomSheet';

// Import navigation service
import navigationService from '../services/NavigationService';

interface ScreenProps {
    navigation: any;
    navigateToTab: (tabName: string) => void;
}

const TabNavigator: React.FC = () => {
    const { theme } = useTheme();
    const insets = useSafeAreaInsets();
    const navigation = useNavigation();
    const quickActionsRef = useRef<any>(null);
    const addCustomerSheetRef = useRef<any>(null);
    const [currentTab, setCurrentTab] = useState<string>('Dashboard');

    const handlePlusPress = (): void => {
        LoggingService.info('Plus button pressed - opening quick actions', 'NAVIGATION');
        quickActionsRef.current?.expand();
    };

    // Fast tab navigation function
    const navigateToTab = useCallback((tabName: string): void => {
        LoggingService.info(`Fast navigation to ${tabName}`, 'NAVIGATION');
        setCurrentTab(tabName);
    }, []);

    // Register tab navigation handler with NavigationService
    useEffect(() => {
        navigationService.setTabNavigationHandler(navigateToTab);

        // Cleanup on unmount
        return () => {
            navigationService.setTabNavigationHandler(() => {});
        };
    }, [navigateToTab]);

    const handleQuickAction = (actionId: string): void => {
        LoggingService.info('Quick action selected', 'NAVIGATION', { actionId });

        // Close the quick actions sheet first
        quickActionsRef.current?.close();

        // Add a small delay to ensure the bottom sheet closes before navigation
        setTimeout(() => {
            // Handle different quick actions
            switch (actionId) {
                case 'add-product':
                    // Navigate to Add Product screen using NavigationService
                    LoggingService.info('Navigating to AddProduct', 'NAVIGATION');
                    try {
                        navigationService.navigate('AddProduct');
                        LoggingService.info('Navigation to AddProduct successful', 'NAVIGATION');
                    } catch (error) {
                        LoggingService.error('Navigation to AddProduct failed', 'NAVIGATION', error as Error);
                    }
                    break;
                case 'add-order':
                    // Navigate to Add Order screen using NavigationService
                    LoggingService.info('Navigating to AddOrder', 'NAVIGATION');
                    try {
                        navigationService.navigate('AddOrder');
                        LoggingService.info('Navigation to AddOrder successful', 'NAVIGATION');
                    } catch (error) {
                        LoggingService.error('Navigation to AddOrder failed', 'NAVIGATION', error as Error);
                    }
                    break;
                case 'scan':
                    // Navigate to Scan tab
                    LoggingService.info('Navigating to Scan tab', 'NAVIGATION');
                    navigateToTab('Scan');
                    break;
                case 'add-customer':
                    // Open AddCustomerBottomSheet globally
                    addCustomerSheetRef.current?.expand();
                    break;
                case 'financial-analytics':
                    // Navigate to Financial tab
                    LoggingService.info('Navigating to Financial Analytics', 'NAVIGATION');
                    navigateToTab('Financial');
                    break;
                default:
                    LoggingService.warn('Unknown action', 'NAVIGATION', { actionId });
            }
        }, 300); // 300ms delay
    };

    const renderCurrentScreen = (): React.ReactElement => {
        const screenProps: ScreenProps = {
            navigation,
            navigateToTab
        };

        switch (currentTab) {
            case 'Dashboard':
                return <DashboardScreen {...screenProps} />;
            case 'Scan':
                return <ScanScreen {...screenProps} />;
            case 'Orders':
                return <OrdersScreen {...screenProps} />;
            case 'Products':
                return <ProductsScreen {...screenProps} />;
            case 'Financial':
                return <FinancialScreen {...screenProps} />;
            case 'Settings':
            case 'Profile':
                return <ProfileScreen {...(screenProps as any)} />;
            case 'Customers':
                return <CustomersScreen {...(screenProps as any)} />;
            default:
                return <DashboardScreen {...screenProps} />;
        }
    };

    return (
        <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
            {/* Current Screen */}
            {renderCurrentScreen()}

            {/* Custom Bottom Navigation */}
            <BottomNavBar
                navigation={navigation}
                currentRoute={currentTab}
                onTabPress={setCurrentTab}
                onPlusPress={handlePlusPress}
                style={{}}
                backgroundColor={theme.colors.surface}
            />

            {/* Global Quick Actions Bottom Sheet */}
            {React.createElement(QuickActionsBottomSheet as any, {
                ref: quickActionsRef,
                onActionPress: handleQuickAction
            })}

            {/* Global Add Customer Bottom Sheet */}
            {React.createElement(AddCustomerBottomSheet as any, {
                ref: addCustomerSheetRef
            })}
        </View>
    );
};

export default TabNavigator;