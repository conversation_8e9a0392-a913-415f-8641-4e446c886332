import React, { useCallback } from 'react';
import { Alert } from 'react-native';
import { ERROR_MESSAGES } from '../config/constants';

/**
 * Error types for better categorization
 */
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  STORAGE: 'STORAGE',
  PERMISSION: 'PERMISSION',
  BUSINESS_LOGIC: 'BUSINESS_LOGIC',
  UNKNOWN: 'UNKNOWN',
} as const;

export type ErrorType = typeof ErrorTypes[keyof typeof ErrorTypes];

/**
 * Logger utility for development and production
 */
export class Logger {
  static isDevelopment: boolean = __DEV__;

  static log(level: string, message: string, data: any = null): void {
    if (this.isDevelopment) {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [${level}] ${message}`;

      switch (level) {
        case 'ERROR':
          console.error(logMessage, data);
          break;
        case 'WARN':
          console.warn(logMessage, data);
          break;
        case 'INFO':
          console.info(logMessage, data);
          break;
        default:
          console.log(logMessage, data);
      }
    }

    // In production, you might want to send logs to a service
    // this.sendToLoggingService(level, message, data);
  }

  static error(message: string, data: any = null): void {
    this.log('ERROR', message, data);
  }

  static warn(message: string, data: any = null): void {
    this.log('WARN', message, data);
  }

  static info(message: string, data: any = null): void {
    this.log('INFO', message, data);
  }

  static debug(message: string, data: any = null): void {
    this.log('DEBUG', message, data);
  }
}

/**
 * Custom Error classes for better error handling
 */
export class AppError extends Error {
  public type: ErrorType;
  public originalError: Error | null;
  public timestamp: string;

  constructor(message: string, type: ErrorType = ErrorTypes.UNKNOWN, originalError: Error | null = null) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
  }
}

export class NetworkError extends AppError {
  constructor(message: string, originalError: Error | null = null) {
    super(message, ErrorTypes.NETWORK, originalError);
    this.name = 'NetworkError';
  }
}

export class ValidationError extends AppError {
  public field: string | null;

  constructor(message: string, field: string | null = null, originalError: Error | null = null) {
    super(message, ErrorTypes.VALIDATION, originalError);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class StorageError extends AppError {
  constructor(message: string, originalError: Error | null = null) {
    super(message, ErrorTypes.STORAGE, originalError);
    this.name = 'StorageError';
  }
}

/**
 * Global error handler
 */
export class ErrorHandler {
  static handle(error: Error | AppError, context: string = 'Unknown'): string {
    Logger.error(`Error in ${context}:`, {
      message: error.message,
      stack: error.stack,
      type: (error as AppError).type || ErrorTypes.UNKNOWN,
    });

    // Determine user-friendly message
    let userMessage: string = ERROR_MESSAGES.GENERIC_ERROR;

    if (error instanceof AppError) {
      switch (error.type) {
        case ErrorTypes.NETWORK:
          userMessage = ERROR_MESSAGES.NETWORK_ERROR;
          break;
        case ErrorTypes.VALIDATION:
          userMessage = ERROR_MESSAGES.VALIDATION_ERROR;
          break;
        case ErrorTypes.STORAGE:
          userMessage = ERROR_MESSAGES.SAVE_ERROR;
          break;
        case ErrorTypes.PERMISSION:
          userMessage = ERROR_MESSAGES.PERMISSION_ERROR;
          break;
        default:
          userMessage = error.message || ERROR_MESSAGES.GENERIC_ERROR;
      }
    }

    return userMessage;
  }

  static showAlert(error: Error | AppError, context: string = 'Error', onPress: (() => void) | null = null): void {
    const message = this.handle(error, context);

    Alert.alert(
      'Error',
      message,
      [
        {
          text: 'OK',
          onPress: onPress || (() => {}),
        },
      ]
    );
  }

  static async handleAsync<T>(asyncFunction: () => Promise<T>, context: string = 'Async Operation'): Promise<T> {
    try {
      return await asyncFunction();
    } catch (error) {
      this.handle(error as Error, context);
      throw error;
    }
  }
}

export interface UseErrorHandlerReturn {
  handleError: (error: Error | AppError, context?: string) => string;
  showErrorAlert: (error: Error | AppError, context?: string, onPress?: (() => void) | null) => void;
  handleAsyncError: <T>(asyncFunction: () => Promise<T>, context?: string) => Promise<T>;
}

/**
 * Error boundary hook for React components
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const handleError = useCallback((error: Error | AppError, context: string = 'Component'): string => {
    return ErrorHandler.handle(error, context);
  }, []);

  const showErrorAlert = useCallback((error: Error | AppError, context: string = 'Error', onPress: (() => void) | null = null): void => {
    ErrorHandler.showAlert(error, context, onPress);
  }, []);

  const handleAsyncError = useCallback(async <T>(asyncFunction: () => Promise<T>, context: string = 'Async Operation'): Promise<T> => {
    return ErrorHandler.handleAsync(asyncFunction, context);
  }, []);

  return {
    handleError,
    showErrorAlert,
    handleAsyncError,
  };
};

interface NetworkErrorResponse {
  response?: {
    status: number;
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Network error handler
 */
export const handleNetworkError = (error: NetworkErrorResponse): never => {
  if (!error.response) {
    // Network error
    throw new NetworkError(ERROR_MESSAGES.NETWORK_ERROR, error as Error);
  } else if (error.response.status >= 400 && error.response.status < 500) {
    // Client error
    throw new AppError(
      error.response.data?.message || 'Client error occurred',
      ErrorTypes.VALIDATION,
      error as Error
    );
  } else if (error.response.status >= 500) {
    // Server error
    throw new AppError(
      'Server error occurred. Please try again later.',
      ErrorTypes.NETWORK,
      error as Error
    );
  }

  throw new AppError(error.message || ERROR_MESSAGES.GENERIC_ERROR, ErrorTypes.UNKNOWN, error as Error);
};