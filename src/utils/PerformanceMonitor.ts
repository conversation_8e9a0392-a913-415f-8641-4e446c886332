/**
 * PerformanceMonitor - Monitors app performance and provides metrics
 * 
 * @description Tracks performance metrics, memory usage, and provides
 * optimization recommendations for enterprise-level quality.
 */

interface NavigationMetric {
  screen: string;
  duration: number;
  timestamp: number;
}

interface RenderMetric {
  component: string;
  duration: number;
  timestamp: number;
}

interface MemoryMetric {
  used: number;
  total: number;
  limit: number;
  timestamp: number;
}

interface PerformanceMetrics {
  navigationTimes: NavigationMetric[];
  renderTimes: RenderMetric[];
  memoryUsage: MemoryMetric[];
  errorCount: number;
  crashCount: number;
  lastUpdate: number;
}

interface PerformanceSummary {
  uptime: number;
  avgNavigationTime: number;
  avgRenderTime: number;
  errorCount: number;
  crashCount: number;
  memoryUsage: {
    used: number;
    total: number;
    usagePercent: number;
  } | null;
  qualityScore: number;
}

declare global {
  interface Performance {
    memory?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    };
  }
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private startTime: number;

  constructor() {
    this.metrics = {
      navigationTimes: [],
      renderTimes: [],
      memoryUsage: [],
      errorCount: 0,
      crashCount: 0,
      lastUpdate: Date.now()
    };
    this.startTime = Date.now();
  }

  /**
   * Track navigation performance
   */
  trackNavigation(screenName: string, startTime: number, endTime: number): void {
    const duration = endTime - startTime;
    this.metrics.navigationTimes.push({
      screen: screenName,
      duration,
      timestamp: Date.now()
    });

    // Keep only last 50 entries
    if (this.metrics.navigationTimes.length > 50) {
      this.metrics.navigationTimes = this.metrics.navigationTimes.slice(-50);
    }

    // Log slow navigation
    if (duration > 1000) {
      console.warn(`Slow navigation to ${screenName}: ${duration}ms`);
    }
  }

  /**
   * Track render performance
   */
  trackRender(componentName: string, renderTime: number): void {
    this.metrics.renderTimes.push({
      component: componentName,
      duration: renderTime,
      timestamp: Date.now()
    });

    // Keep only last 100 entries
    if (this.metrics.renderTimes.length > 100) {
      this.metrics.renderTimes = this.metrics.renderTimes.slice(-100);
    }
  }

  /**
   * Track memory usage
   */
  trackMemoryUsage(): void {
    if (global.performance && global.performance.memory) {
      const memory: MemoryMetric = {
        used: global.performance.memory.usedJSHeapSize,
        total: global.performance.memory.totalJSHeapSize,
        limit: global.performance.memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };

      this.metrics.memoryUsage.push(memory);

      // Keep only last 20 entries
      if (this.metrics.memoryUsage.length > 20) {
        this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-20);
      }

      // Warn if memory usage is high
      const usagePercent = (memory.used / memory.limit) * 100;
      if (usagePercent > 80) {
        console.warn(`High memory usage: ${usagePercent.toFixed(1)}%`);
      }
    }
  }

  /**
   * Track errors
   */
  trackError(error: Error, context: string = ''): void {
    this.metrics.errorCount++;
    console.error(`Performance Monitor - Error in ${context}:`, error);
  }

  /**
   * Track crashes
   */
  trackCrash(error: Error, context: string = ''): void {
    this.metrics.crashCount++;
    console.error(`Performance Monitor - Crash in ${context}:`, error);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): PerformanceSummary {
    const now = Date.now();
    const uptime = now - this.startTime;
    
    const avgNavigationTime = this.metrics.navigationTimes.length > 0
      ? this.metrics.navigationTimes.reduce((sum, nav) => sum + nav.duration, 0) / this.metrics.navigationTimes.length
      : 0;

    const avgRenderTime = this.metrics.renderTimes.length > 0
      ? this.metrics.renderTimes.reduce((sum, render) => sum + render.duration, 0) / this.metrics.renderTimes.length
      : 0;

    const currentMemory = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : null;

    return {
      uptime,
      avgNavigationTime: Math.round(avgNavigationTime),
      avgRenderTime: Math.round(avgRenderTime),
      errorCount: this.metrics.errorCount,
      crashCount: this.metrics.crashCount,
      memoryUsage: currentMemory ? {
        used: Math.round(currentMemory.used / 1024 / 1024), // MB
        total: Math.round(currentMemory.total / 1024 / 1024), // MB
        usagePercent: Math.round((currentMemory.used / currentMemory.limit) * 100)
      } : null,
      qualityScore: this.calculateQualityScore()
    };
  }

  /**
   * Calculate quality score (0-100)
   */
  private calculateQualityScore(): number {
    let score = 100;

    // Deduct for slow navigation
    const avgNavTime = this.metrics.navigationTimes.length > 0
      ? this.metrics.navigationTimes.reduce((sum, nav) => sum + nav.duration, 0) / this.metrics.navigationTimes.length
      : 0;
    
    if (avgNavTime > 500) score -= 10;
    if (avgNavTime > 1000) score -= 20;

    // Deduct for errors
    if (this.metrics.errorCount > 0) score -= Math.min(this.metrics.errorCount * 2, 20);
    
    // Deduct for crashes
    if (this.metrics.crashCount > 0) score -= Math.min(this.metrics.crashCount * 10, 30);

    // Deduct for high memory usage
    const currentMemory = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : null;
    
    if (currentMemory) {
      const usagePercent = (currentMemory.used / currentMemory.limit) * 100;
      if (usagePercent > 70) score -= 5;
      if (usagePercent > 85) score -= 15;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const summary = this.getPerformanceSummary();

    if (summary.avgNavigationTime > 500) {
      recommendations.push('Consider optimizing navigation performance');
    }

    if (summary.errorCount > 5) {
      recommendations.push('Review error handling and fix recurring issues');
    }

    if (summary.memoryUsage && summary.memoryUsage.usagePercent > 80) {
      recommendations.push('Optimize memory usage - consider cleanup and optimization');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is excellent! No optimizations needed.');
    }

    return recommendations;
  }

  /**
   * Reset metrics
   */
  reset(): void {
    this.metrics = {
      navigationTimes: [],
      renderTimes: [],
      memoryUsage: [],
      errorCount: 0,
      crashCount: 0,
      lastUpdate: Date.now()
    };
    this.startTime = Date.now();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;