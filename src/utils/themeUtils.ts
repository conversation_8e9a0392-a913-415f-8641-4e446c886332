interface ThemeColors {
  primary: string;
  onSurfaceVariant: string;
  outline: string;
  background: string;
}

interface Theme {
  colors: ThemeColors;
  mode?: 'light' | 'dark';
}

export function getThemeWithFallback(theme: Theme | null | undefined): Theme {
  return theme && theme.colors
    ? theme
    : { 
        colors: { 
          primary: '#2563EB', 
          onSurfaceVariant: '#64748B', 
          outline: '#eee', 
          background: '#fff' 
        } 
      };
}