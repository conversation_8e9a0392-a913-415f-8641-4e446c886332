import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Linking, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Surface,
  Button,
  TextInput,
  List,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';
import UnifiedTextInput from '../components/UnifiedTextInput';
import LoggingService from '../services/LoggingService';

interface SupportForm {
  subject: string;
  message: string;
  priority: string;
  category: string;
}

interface Priority {
  id: string;
  label: string;
  color: string;
}

interface Category {
  id: string;
  label: string;
  icon: string;
}

interface ContactMethod {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
  availability: string;
  responseTime: string;
  action: () => void;
}

const ContactSupportScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const [supportForm, setSupportForm] = useState<SupportForm>({
    subject: '',
    message: '',
    priority: 'medium',
    category: 'general',
  });

  const priorities: Priority[] = [
    { id: 'low', label: 'Low', color: '#4CAF50' },
    { id: 'medium', label: 'Medium', color: '#FF9800' },
    { id: 'high', label: 'High', color: '#F44336' },
  ];

  const categories: Category[] = [
    { id: 'general', label: 'General', icon: 'help-circle' },
    { id: 'technical', label: 'Technical Issue', icon: 'bug' },
    { id: 'billing', label: 'Billing', icon: 'credit-card' },
    { id: 'feature', label: 'Feature Request', icon: 'lightbulb' },
  ];

  const contactMethods: ContactMethod[] = [
    {
      id: 'email',
      title: 'Email Support',
      subtitle: '<EMAIL>',
      description: 'Get help via email within 24 hours',
      icon: 'email',
      color: '#4285F4',
      availability: '24/7',
      responseTime: '< 24 hours',
      action: () => {
        LoggingService.info('Opening email support', 'SUPPORT');
        Linking.openURL('mailto:<EMAIL>?subject=Bakery App Support Request');
      },
    },
    {
      id: 'phone',
      title: 'Phone Support',
      subtitle: '+1 (800) BAKERY-1',
      description: 'Call us Mon-Fri, 9 AM - 6 PM EST',
      icon: 'phone',
      color: '#34A853',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: 'Immediate',
      action: () => {
        LoggingService.info('Opening phone support', 'SUPPORT');
        Linking.openURL('tel:+18002253791');
      },
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp Support',
      subtitle: '+1 (800) BAKERY-2',
      description: 'Message us on WhatsApp for quick help',
      icon: 'whatsapp',
      color: '#25D366',
      availability: '24/7',
      responseTime: '< 1 hour',
      action: () => {
        LoggingService.info('Opening WhatsApp support', 'SUPPORT');
        Linking.openURL('https://wa.me/18002253792?text=Hello, I need help with the Bakery Management App');
      },
    },
    {
      id: 'chat',
      title: 'Live Chat',
      subtitle: 'Available now',
      description: 'Chat with our support team instantly',
      icon: 'chat',
      color: '#FF6B35',
      availability: 'Mon-Fri 9AM-6PM EST',
      responseTime: '< 5 minutes',
      action: () => {
        LoggingService.info('Live chat requested', 'SUPPORT');
        Alert.alert('Live Chat', 'Live chat feature coming soon! For immediate help, please use email or phone support.');
      },
    },
    {
      id: 'community',
      title: 'Community Forum',
      subtitle: 'community.bakeryapp.com',
      description: 'Connect with other users and get help',
      icon: 'forum',
      color: '#9C27B0',
      availability: '24/7',
      responseTime: 'Community driven',
      action: () => {
        LoggingService.info('Opening community forum', 'SUPPORT');
        Linking.openURL('https://community.bakeryapp.com');
      },
    },
    {
      id: 'video',
      title: 'Video Call Support',
      subtitle: 'Schedule a call',
      description: 'Book a video call for personalized help',
      icon: 'video',
      color: '#FF5722',
      availability: 'By appointment',
      responseTime: 'Scheduled',
      action: () => {
        LoggingService.info('Video support requested', 'SUPPORT');
        Alert.alert('Video Support', 'Video call support available for premium users. Contact us to schedule a session.');
      },
    },
  ];

  const handleSubmitTicket = (): void => {
    if (!supportForm.subject.trim() || !supportForm.message.trim()) {
      Alert.alert('Error', 'Please fill in both subject and message fields.');
      return;
    }

    LoggingService.info('Support ticket submitted', 'SUPPORT', {
      category: supportForm.category,
      priority: supportForm.priority,
      subject: supportForm.subject
    });

    // Simulate ticket submission
    Alert.alert(
      'Support Ticket Submitted',
      'Your support ticket has been submitted successfully. We\'ll get back to you within 24 hours.',
      [
        {
          text: 'OK',
          onPress: () => {
            setSupportForm({
              subject: '',
              message: '',
              priority: 'medium',
              category: 'general',
            });
          }
        }
      ]
    );
  };

  const updateSupportForm = (field: keyof SupportForm, value: string): void => {
    setSupportForm(prev => ({ ...prev, [field]: value }));
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Contact Support"
        subtitle="Get help from our support team"
        showSearch={false}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Contact Methods */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Quick Contact
          </Text>

          {contactMethods.map((method) => (
            <List.Item
              key={method.id}
              title={method.title}
              description={method.description}
              left={() => (
                <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                  <Icon name={method.icon} size={24} color={theme.colors.primary} />
                </View>
              )}
              right={() => (
                <View style={styles.contactInfo}>
                  <Text variant="bodySmall" style={{ color: theme.colors.primary, fontWeight: '600' }}>
                    {method.subtitle}
                  </Text>
                </View>
              )}
              onPress={method.action}
              style={styles.contactMethod}
            />
          ))}
        </Surface>

        {/* Support Ticket Form */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Submit Support Ticket
          </Text>

          {/* Category Selection */}
          <Text variant="bodyMedium" style={[styles.fieldLabel, { color: theme.colors.onSurface }]}>
            Category
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
            {categories.map((category) => (
              <Chip
                key={category.id}
                selected={supportForm.category === category.id}
                onPress={() => updateSupportForm('category', category.id)}
                icon={category.icon}
                style={[
                  styles.chip,
                  supportForm.category === category.id && { backgroundColor: theme.colors.primary }
                ]}
                textStyle={{
                  color: supportForm.category === category.id ? theme.colors.onPrimary : theme.colors.onSurface
                }}
              >
                {String(category.label ?? '')}
              </Chip>
            ))}
          </ScrollView>

          {/* Priority Selection */}
          <Text variant="bodyMedium" style={[styles.fieldLabel, { color: theme.colors.onSurface }]}>
            Priority
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
            {priorities.map((priority) => (
              <Chip
                key={priority.id}
                selected={supportForm.priority === priority.id}
                onPress={() => updateSupportForm('priority', priority.id)}
                style={[
                  styles.chip,
                  supportForm.priority === priority.id && { backgroundColor: priority.color }
                ]}
                textStyle={{
                  color: supportForm.priority === priority.id ? '#FFFFFF' : theme.colors.onSurface
                }}
              >
                {String(priority.label ?? '')}
              </Chip>
            ))}
          </ScrollView>

          <UnifiedTextInput
            label="Subject *"
            value={supportForm.subject}
            onChangeText={(text: string) => updateSupportForm('subject', text)}
            style={styles.input}
            placeholder="Brief description of your issue"
            minLength={0}
            maxLength={200}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            rightAffix=""
          />

          <UnifiedTextInput
            label="Message *"
            value={supportForm.message}
            onChangeText={(text: string) => updateSupportForm('message', text)}
            multiline
            numberOfLines={6}
            style={styles.textArea}
            placeholder="Please provide detailed information about your issue..."
            minLength={0}
            maxLength={1000}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            rightAffix=""
          />

          <Button
            mode="contained"
            onPress={handleSubmitTicket}
            style={styles.submitButton}
            icon="send"
          >
            Submit Ticket
          </Button>
        </Surface>

        {/* Support Hours */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
          <View style={styles.supportHours}>
            <Icon name="clock-outline" size={32} color={theme.colors.onPrimaryContainer} />
            <View style={styles.hoursText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Support Hours
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, marginTop: 4 }}>
                Monday - Friday: 9:00 AM - 6:00 PM EST
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Saturday - Sunday: 10:00 AM - 4:00 PM EST
              </Text>
            </View>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  contactMethod: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  contactInfo: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  fieldLabel: {
    fontWeight: '600',
    marginBottom: SPACING.xs,
    marginTop: SPACING.sm,
  },
  chipsContainer: {
    marginBottom: SPACING.md,
  },
  chip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  input: {
    marginBottom: SPACING.md,
  },
  textArea: {
    marginBottom: SPACING.lg,
  },
  submitButton: {
    marginTop: SPACING.sm,
  },
  supportHours: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hoursText: {
    flex: 1,
    marginLeft: SPACING.md,
  },
});

export default ContactSupportScreen;