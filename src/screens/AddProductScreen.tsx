import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Switch,
  Chip,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';
import navigationService from '../services/NavigationService';
import UnifiedTextInput from '../components/UnifiedTextInput';
import UnifiedFormSection from '../components/UnifiedFormSection';
import LoggingService from '../services/LoggingService';

interface AddProductScreenProps {
  route?: {
    params?: {
      product?: any;
      barcode?: string;
    };
  };
}

interface ProductData {
  name: string;
  description: string;
  price: string;
  cost: string;
  category: string;
  stock: string;
  sku: string;
  barcode: string;
  image: string | null;
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
}

interface ProductField {
  key: string;
  label: string;
  type: string;
  value: string;
  onChange: (val: string) => void;
  required: boolean;
  validation?: (val: string) => string;
  multiline?: boolean;
  inputProps?: any;
}

const AddProductScreen: React.FC<AddProductScreenProps> = ({ route }) => {
  const { theme } = useTheme();
  const { actions } = useData();
  const navigation = useNavigation();

  // Get product from route params for editing
  const editingProduct = route?.params?.product;
  const isEditing = !!editingProduct;

  // Product form state
  const [productData, setProductData] = useState<ProductData>({
    name: editingProduct?.name || '',
    description: editingProduct?.description || '',
    price: editingProduct?.price?.toString() || '',
    cost: editingProduct?.cost?.toString() || '',
    category: editingProduct?.category || '',
    stock: editingProduct?.stock?.toString() || '',
    sku: editingProduct?.sku || '',
    barcode: editingProduct?.barcode || route?.params?.barcode || '',
    image: editingProduct?.image || null,
    isActive: editingProduct?.isActive ?? true,
    isFeatured: editingProduct?.isFeatured ?? false,
    tags: editingProduct?.tags || [],
  });

  const [newTag, setNewTag] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Auto-generate SKU and Barcode when product name changes (only for new products)
  React.useEffect(() => {
    if (!isEditing && productData.name && !productData.sku) {
      const generateSKU = (): string => {
        const prefix = productData.category ? productData.category.substring(0, 3).toUpperCase() : 'PRD';
        const timestamp = Date.now().toString().slice(-6);
        const nameCode = productData.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
        return `${prefix}-${nameCode}-${timestamp}`;
      };

      const generateBarcode = (): string => {
        // Generate a 13-digit EAN-13 barcode
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp.slice(-7)}${random}000`;
      };

      setProductData(prev => ({
        ...prev,
        sku: generateSKU(),
        barcode: productData.barcode || generateBarcode()
      }));
    }
  }, [productData.name, productData.category, isEditing, productData.barcode]);

  // Predefined categories
  const categories = [
    'Cakes', 'Pastries', 'Bread', 'Cookies', 'Beverages', 'Desserts', 'Seasonal'
  ];

  const validateNumber = (val: string): boolean => !isNaN(Number(val)) && Number(val) > 0;
  const validateNonNegative = (val: string): boolean => !isNaN(Number(val)) && Number(val) >= 0;

  // Field config for UnifiedFormSection
  const productFields: ProductField[] = [
    {
      key: 'name',
      label: 'Product Name',
      type: 'text',
      value: productData.name,
      onChange: (val: string) => setProductData(prev => ({ ...prev, name: val })),
      required: true,
      validation: (val: string) => val.trim() !== '' ? '' : 'Product name is required',
    },
    {
      key: 'description',
      label: 'Description',
      type: 'text',
      value: productData.description,
      onChange: (val: string) => setProductData(prev => ({ ...prev, description: val })),
      required: false,
      multiline: true,
      inputProps: { numberOfLines: 3 },
    },
    {
      key: 'price',
      label: 'Price',
      type: 'numeric',
      value: productData.price,
      onChange: (val: string) => setProductData(prev => ({ ...prev, price: val })),
      required: true,
      validation: (val: string) => !isNaN(Number(val)) && Number(val) > 0 ? '' : 'Enter a valid price',
    },
    {
      key: 'cost',
      label: 'Cost',
      type: 'numeric',
      value: productData.cost,
      onChange: (val: string) => setProductData(prev => ({ ...prev, cost: val })),
      required: false,
      validation: (val: string) => val === '' || (!isNaN(Number(val)) && Number(val) >= 0) ? '' : 'Enter a valid cost',
    },
    {
      key: 'stock',
      label: 'Stock Quantity',
      type: 'numeric',
      value: productData.stock,
      onChange: (val: string) => setProductData(prev => ({ ...prev, stock: val })),
      required: true,
      validation: (val: string) => !isNaN(Number(val)) && Number(val) >= 0 ? '' : 'Stock must be 0 or more',
    },
    {
      key: 'sku',
      label: 'SKU',
      type: 'text',
      value: productData.sku,
      onChange: (val: string) => setProductData(prev => ({ ...prev, sku: val })),
      required: false,
    },
    {
      key: 'barcode',
      label: 'Barcode',
      type: 'text',
      value: productData.barcode,
      onChange: (val: string) => setProductData(prev => ({ ...prev, barcode: val })),
      required: false,
    },
  ];

  const handleInputChange = (field: keyof ProductData, value: string | boolean | string[]): void => {
    setProductData(prev => ({ ...prev, [field]: value }));
    
    if (typeof value === 'string') {
      let error = '';
      if (field === 'name' && !value.trim()) error = 'Product name is required';
      if (field === 'price' && !validateNumber(value)) error = 'Enter a valid price';
      if (field === 'stock' && !validateNonNegative(value)) error = 'Stock must be 0 or more';
      if (field === 'category' && !value.trim()) error = 'Category is required';
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const addTag = (): void => {
    if (newTag.trim() && !productData.tags.includes(newTag.trim())) {
      setProductData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string): void => {
    setProductData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSave = (): void => {
    try {
      const productToSave = {
        ...productData,
        price: parseFloat(productData.price),
        cost: productData.cost ? parseFloat(productData.cost) : 0,
        stock: parseInt(productData.stock),
        updatedAt: new Date().toISOString(),
      };

      if (isEditing) {
        // Update existing product
        const updatedProduct = {
          ...editingProduct,
          ...productToSave,
        };
        actions.updateProduct(updatedProduct);

        LoggingService.info('Product updated successfully', 'PRODUCT', { productId: editingProduct.id });

        Alert.alert(
          'Success',
          'Product updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new product
        const newProduct = {
          id: Date.now().toString(),
          ...productToSave,
          image: productToSave.image || undefined,
          createdAt: new Date().toISOString(),
        };

        actions.addProduct(newProduct as any);

        LoggingService.info('Product added successfully', 'PRODUCT', { productName: newProduct.name });

        Alert.alert(
          'Success',
          'Product added successfully!',
          [
            {
              text: 'Add Another',
              onPress: () => {
                setProductData({
                  name: '',
                  description: '',
                  price: '',
                  cost: '',
                  category: '',
                  stock: '',
                  sku: '',
                  barcode: '',
                  image: null,
                  isActive: true,
                  isFeatured: false,
                  tags: [],
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      LoggingService.error(`Failed to ${isEditing ? 'update' : 'add'} product`, 'PRODUCT', error as Error);
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} product. Please try again.`);
    }
  };

  const renderCategoryChips = () => (
    <View style={styles.chipContainer}>
      {categories.map((category) => (
        <Chip
          key={category}
          selected={productData.category === category}
          onPress={() => handleInputChange('category', category)}
          style={[
            styles.chip,
            productData.category === category && { backgroundColor: theme.colors.primaryContainer }
          ]}
          textStyle={{
            color: productData.category === category ? theme.colors.onPrimaryContainer : theme.colors.onSurface
          }}
        >
          {String(category ?? '')}
        </Chip>
      ))}
    </View>
  );

  const renderTagSection = () => (
    <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Tags
      </Text>

      <View style={styles.tagInputContainer}>
        <UnifiedTextInput
          label="Add tag"
          value={newTag}
          onChangeText={setNewTag}
          onSubmitEditing={addTag}
          style={styles.tagInput}
          rightIcon="plus"
          onRightIconPress={addTag}
          rightIconDisabled={!newTag.trim()}
          minLength={0}
          maxLength={50}
          validate={() => ''}
          error=""
          onBlur={() => {}}
          rightAffix=""
        />
      </View>

      <View style={styles.tagsContainer}>
        {productData.tags.map((tag, index) => (
          <Chip
            key={index}
            onClose={() => removeTag(tag)}
            style={styles.tag}
          >
            {tag}
          </Chip>
        ))}
      </View>
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? "Edit Product" : "Add Product"}
        subtitle={isEditing ? "Update product details" : "Create a new product"}
        showSearch={false}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            LoggingService.error('Failed to navigate to NotificationsScreen', 'NAVIGATION', error as Error);
          }
        }}
        onProfilePress={() => {
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            LoggingService.error('Failed to navigate to MyProfile', 'NAVIGATION', error as Error);
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Main Product Fields */}
        <UnifiedFormSection
          fields={productFields as any}
          errors={errors}
          onFieldError={() => {}}
          sectionTitle="Product Details"
        />

        {/* Category */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Category *
          </Text>
          {renderCategoryChips()}
          {errors.category && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.category}</Text>}
        </Surface>

        {/* Inventory */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Inventory
          </Text>

          <View style={styles.row}>
            <UnifiedTextInput
              label="Stock Quantity *"
              value={productData.stock}
              onChangeText={(value: string) => handleInputChange('stock', value)}
              type="number"
              required
              minLength={0}
              maxLength={10}
              validate={() => ''}
              error=""
              onBlur={() => {}}
              style={{}}
              rightAffix=""
            />

            <UnifiedTextInput
              label="SKU"
              value={productData.sku}
              onChangeText={(value: string) => handleInputChange('sku', value)}
              type="text"
              minLength={0}
              maxLength={50}
              validate={() => ''}
              error=""
              onBlur={() => {}}
              style={{}}
              rightAffix=""
            />
          </View>
          {errors.stock && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.stock}</Text>}

          <UnifiedTextInput
            label="Barcode"
            value={productData.barcode}
            onChangeText={(value: string) => handleInputChange('barcode', value)}
            type="text"
            minLength={0}
            maxLength={50}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            style={{}}
            rightAffix=""
          />
        </Surface>

        {/* Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Settings
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>Active Product</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Available for sale
              </Text>
            </View>
            <Switch
              value={productData.isActive}
              onValueChange={(value: boolean) => handleInputChange('isActive', value)}
              color={theme.colors.primary}
            />
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>Featured Product</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Highlight in recommendations
              </Text>
            </View>
            <Switch
              value={productData.isFeatured}
              onValueChange={(value: boolean) => handleInputChange('isFeatured', value)}
              color={theme.colors.primary}
            />
          </View>
        </Surface>

        {/* Tags */}
        {renderTagSection()}

        {/* Compact Image Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Image
          </Text>
          <View style={styles.compactImageContainer}>
            {React.createElement(ImagePicker as any, {
              key: productData.image || 'no-image',
              onImageSelected: (imageUri: string) => handleInputChange('image', imageUri),
              currentImage: productData.image,
              placeholder: "Add image"
            })}
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.button, styles.cancelButton]}
            labelStyle={{ color: theme.colors.onSurfaceVariant }}
          >
            Cancel
          </Button>

          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton]}
            buttonColor={theme.colors.primary}
          >
            {isEditing ? 'Update Product' : 'Save Product'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    flex: 1,
  },
  tagInputContainer: {
    marginBottom: 12,
  },
  tagInput: {
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 2,
  },
  compactImageContainer: {
    alignItems: 'center',
  },
});

export default AddProductScreen;