// @ts-nocheck
import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Switch,
  List,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import UnifiedTextInput from '../components/UnifiedTextInput';

const SecuritySettingsScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Security settings state
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    biometricAuth: false,
    autoLock: true,
    autoLockTime: 5, // minutes
    loginNotifications: true,
    dataEncryption: true,
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});

  const handleSecurityToggle = (setting, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: value
    }));

    // Save to settings
    actions.updateSettings({
      securitySettings: {
        ...securitySettings,
        [setting]: value
      }
    });

    // Show confirmation for important security changes
    if (setting === 'twoFactorAuth' && value) {
      Alert.alert(
        'Two-Factor Authentication',
        'Two-factor authentication has been enabled. You will receive a verification code via SMS for future logins.',
        [{ text: 'OK' }]
      );
    }
  };

  const handlePasswordChange = () => {
    const newErrors = {};

    if (!passwordData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    if (!passwordData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (passwordData.newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters';
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Simulate password change
      Alert.alert(
        'Password Changed',
        'Your password has been updated successfully.',
        [
          {
            text: 'OK',
            onPress: () => {
              setPasswordData({
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
              });
            }
          }
        ]
      );
    }
  };

  const handleDataExport = () => {
    Alert.alert(
      'Export Data',
      'This will create an encrypted backup of all your business data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // Simulate data export
            setTimeout(() => {
              Alert.alert('Success', 'Data exported successfully to your device.');
            }, 1000);
          }
        }
      ]
    );
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your business data. This action cannot be undone. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Final Confirmation',
              'Type "DELETE" to confirm data deletion:',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Confirm',
                  style: 'destructive',
                  onPress: () => {
                    actions.clearData();
                    Alert.alert('Data Cleared', 'All data has been deleted.');
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  const SecurityToggleItem = ({ title, description, value, onToggle, icon }) => (
    <List.Item
      title={title}
      description={description}
      titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
      descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
      left={() => (
        <View style={[styles.settingIcon, { backgroundColor: theme.colors.primaryContainer }]}>
          <Icon name={icon} size={24} color={theme.colors.primary} />
        </View>
      )}
      right={() => (
        <Switch
          value={value}
          onValueChange={onToggle}
          color={theme.colors.primary}
        />
      )}
      style={styles.listItem}
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Security Settings"
        subtitle="Manage your account security"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Authentication Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Authentication
          </Text>

          <SecurityToggleItem
            title="Two-Factor Authentication"
            description="Add an extra layer of security with SMS verification"
            value={securitySettings.twoFactorAuth}
            onToggle={(value) => handleSecurityToggle('twoFactorAuth', value)}
            icon="shield-check"
          />

          <Divider style={{ marginVertical: 4 }} />

          <SecurityToggleItem
            title="Biometric Authentication"
            description="Use fingerprint or face recognition to unlock"
            value={securitySettings.biometricAuth}
            onToggle={(value) => handleSecurityToggle('biometricAuth', value)}
            icon="fingerprint"
          />

          <Divider style={{ marginVertical: 4 }} />

          <SecurityToggleItem
            title="Auto Lock"
            description="Automatically lock the app when inactive"
            value={securitySettings.autoLock}
            onToggle={(value) => handleSecurityToggle('autoLock', value)}
            icon="lock-clock"
          />
        </Surface>

        {/* Password Change */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Change Password
          </Text>

          <UnifiedTextInput
            label="Current Password"
            value={passwordData.currentPassword}
            onChangeText={(value) => setPasswordData(prev => ({ ...prev, currentPassword: value }))}
            type="password"
            required
          />
          <UnifiedTextInput
            label="New Password"
            value={passwordData.newPassword}
            onChangeText={(value) => setPasswordData(prev => ({ ...prev, newPassword: value }))}
            type="password"
            required
            minLength={8}
          />
          <UnifiedTextInput
            label="Confirm New Password"
            value={passwordData.confirmPassword}
            onChangeText={(value) => setPasswordData(prev => ({ ...prev, confirmPassword: value }))}
            type="password"
            required
            minLength={8}
          />

          <Button
            mode="contained"
            onPress={handlePasswordChange}
            style={styles.passwordButton}
            icon="check"
          >
            Update Password
          </Button>
        </Surface>

        {/* Privacy & Data */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Privacy & Data
          </Text>

          <SecurityToggleItem
            title="Login Notifications"
            description="Get notified of new login attempts"
            value={securitySettings.loginNotifications}
            onToggle={(value) => handleSecurityToggle('loginNotifications', value)}
            icon="bell-alert"
          />

          <Divider style={{ marginVertical: 4 }} />

          <SecurityToggleItem
            title="Data Encryption"
            description="Encrypt sensitive business data"
            value={securitySettings.dataEncryption}
            onToggle={(value) => handleSecurityToggle('dataEncryption', value)}
            icon="shield-lock"
          />

          <Divider style={{ marginVertical: 4 }} />

          <List.Item
            title="Export Data"
            description="Create an encrypted backup of your data"
            titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
            descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
            left={() => (
              <View style={[styles.settingIcon, { backgroundColor: theme.colors.secondaryContainer }]}>
                <Icon name="download" size={24} color={theme.colors.secondary} />
              </View>
            )}
            right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
            onPress={handleDataExport}
            style={styles.listItem}
          />

          <Divider style={{ marginVertical: 4 }} />

          <List.Item
            title="Clear All Data"
            description="Permanently delete all business data"
            titleStyle={{ fontWeight: '600', color: theme.colors.error }}
            descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
            left={() => (
              <View style={[styles.settingIcon, { backgroundColor: theme.colors.errorContainer }]}>
                <Icon name="delete-forever" size={24} color={theme.colors.error} />
              </View>
            )}
            right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
            onPress={handleClearData}
            style={styles.listItem}
          />
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  settingIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  listItem: {
    paddingVertical: 8,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 8,
    marginLeft: 4,
  },
  passwordButton: {
    marginTop: 16,
  },
});

export default SecuritySettingsScreen;
