import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, ScrollView, View, StyleSheet, Alert, Image, RefreshControl, ListRenderItem } from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Text,
  Button,
  Chip,
  Surface,
  IconButton,
  Menu,
  FAB,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import LoggingService from '../services/LoggingService';
import CommonHeader from '../components/CommonHeader';
import Chips from '../components/Chips';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import UnifiedInfoCard from '../components/UnifiedInfoCard';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS, SHADOWS, getBorderColor, getThemedShadow } from '../theme/designTokens';
import { BottomSheetManager } from '../components/BulkOperations';

interface ProductsScreenProps {
  navigation: any;
}

interface Product {
  id: string | number;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image?: string;
  icon: string;
  sku?: string;
}

const ProductsScreen: React.FC<ProductsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [menuVisible, setMenuVisible] = useState<Record<string, boolean>>({});
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const categories = ['All', 'Bread', 'Pastries', 'Cakes', 'Beverages'];

  // Pull to refresh functionality
  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Products data refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.warn('Failed to refresh products data', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const filteredProducts = useMemo(() => {
    return state.products.filter((product: any) => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [state.products, searchQuery, selectedCategory]);

  const handleEditProduct = (product: any): void => {
    LoggingService.info('Navigating to Edit Product page', 'SCREEN', { productId: product.id });
    try {
      // For now, navigate to add product page - could be enhanced to pass product data
      navigationService.navigate('AddProduct', { product });
    } catch (error) {
      LoggingService.error('Failed to navigate to edit product', 'SCREEN', error as Error);
    }
  };

  const handleDeleteProduct = (product: any): void => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${product.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteProduct(product.id),
        },
      ]
    );
  };

  const handleAddProduct = (): void => {
    LoggingService.info('Navigating to Add Product page', 'SCREEN');
    try {
      navigationService.navigate('AddProduct');
    } catch (error) {
      LoggingService.error('Failed to navigate to AddProduct', 'SCREEN', error as Error);
    }
  };

  const handleBulkOperations = (): void => {
    const bulkConfig = {
      type: 'products',
      items: filteredProducts.map((product: any) => ({
        id: product.id,
        title: product.name,
        subtitle: `SKU: ${product.sku} • ৳${product.price} • ${product.stock} in stock`,
        data: product,
      })),
    };

    BottomSheetManager.getInstance().showBulkOperations(bulkConfig);
  };

  const handleProductPress = (product: any): void => {
    BottomSheetManager.getInstance().showProductDetails(product);
  };

  const handleAddToOrder = (product: any): void => {
    if (product.stock > 0) {
      // This would typically navigate to order creation or add to cart
      Alert.alert('Add to Order', `${product.name} would be added to a new order`);
    } else {
      Alert.alert('Out of Stock', 'This product is currently out of stock');
    }
  };

  const toggleMenu = (productId: string | number): void => {
    setMenuVisible(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const closeMenu = (productId: string | number): void => {
    setMenuVisible(prev => ({
      ...prev,
      [productId]: false
    }));
  };

  const renderProductCard: ListRenderItem<any> = useCallback(({ item: product }) => (
    <Surface
      style={[
        styles.productCard,
        {
          backgroundColor: theme.colors.surface,
          borderColor: getBorderColor(theme),
        }
      ]}
      elevation={0}
      onTouchEnd={() => handleProductPress(product)}
    >
      {product.image && (
        <Image source={{ uri: product.image }} style={styles.productImage} />
      )}
      <View style={styles.productContent}>
        <View style={styles.productHeader}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
            <Icon name={product.icon} size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.productInfo}>
            <Text variant="titleMedium" style={{ fontWeight: '600' }}>{product.name}</Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
              {product.description}
            </Text>
            <View style={styles.productMeta}>
              <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700', marginTop: 8 }}>
                ৳{product.price.toFixed(2)}
              </Text>
              <View style={styles.stockContainer}>
                <View
                  style={[
                    styles.stockDot,
                    {
                      backgroundColor: product.stock > 10 ? theme.colors.tertiary :
                                      product.stock > 5 ? theme.colors.secondary :
                                      theme.colors.error
                    }
                  ]}
                />
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }}>
                  {product.stock} in stock
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.actionButtons}>
          <Menu
            visible={menuVisible[product.id] || false}
            onDismiss={() => closeMenu(product.id)}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                iconColor={theme.colors.onSurfaceVariant}
                onPress={() => toggleMenu(product.id)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                closeMenu(product.id);
                handleEditProduct(product);
              }}
              title="Edit"
              leadingIcon="pencil"
            />
            <Menu.Item
              onPress={() => {
                closeMenu(product.id);
                handleDeleteProduct(product);
              }}
              title="Delete"
              leadingIcon="delete"
            />
          </Menu>

          <IconButton
            icon="plus"
            size={24}
            iconColor={product.stock > 0 ? theme.colors.primary : theme.colors.onSurfaceVariant}
            style={[
              styles.addButton,
              {
                backgroundColor: product.stock > 0 ? theme.colors.primary + '15' : theme.colors.surfaceVariant
              }
            ]}
            onPress={() => handleAddToOrder(product)}
            disabled={product.stock === 0}
          />
        </View>
      </View>
    </Surface>
  ), [theme, handleEditProduct, handleDeleteProduct, handleAddToOrder, menuVisible, toggleMenu, closeMenu]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Products"
        subtitle="Manage your inventory"
        searchPlaceholder="Search products..."
        searchType="products"
        searchData={state.products}
        searchFields={["name", "description", "category"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(product: any) => {
          LoggingService.debug('Product selected from search', 'SCREEN', product);
          handleEditProduct(product);
        }}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            LoggingService.error('Failed to navigate to NotificationsScreen', 'SCREEN', error as Error);
          }
        }}
        onProfilePress={() => {
          LoggingService.info('Profile pressed - navigating to MyProfile', 'SCREEN');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            LoggingService.error('Failed to navigate to MyProfile', 'SCREEN', error as Error);
          }
        }}
      />

      <FlatList
        data={filteredProducts}
        renderItem={renderProductCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {/* Category Filter */}
            <Chips
              filters={categories as any}
              selectedFilter={selectedCategory}
              onFilterChange={(filter) => setSelectedCategory(String(filter))}
              showCounts={true}
              data={state.products as any}
              countField="category"
              style={{ marginBottom: 16 }}
              chipStyle={{}}
            />

            {/* Stats Row */}
            <View style={styles.statsRow}>
              <View style={styles.statCardWrapper}>
                <UnifiedInfoCard
                  type="stat"
                  icon="food-variant"
                  iconColor={theme.colors.primary}
                  iconBackground={theme.colors.primary + '15'}
                  value={filteredProducts.length.toString()}
                  title="Products"
                  subtitle=""
                  description=""
                  elevation={1}
                  onPress={() => {}}
                  disabled={false}
                  style={{}}
                  data={{}}
                  growth={0}
                  status=""
                  statusColor=""
                />
              </View>
              <View style={styles.statCardWrapper}>
                <UnifiedInfoCard
                  type="stat"
                  icon="package-variant"
                  iconColor={theme.colors.secondary}
                  iconBackground={theme.colors.secondary + '15'}
                  value={filteredProducts.reduce((sum: number, product: any) => sum + product.stock, 0).toString()}
                  title="Total Stock"
                  subtitle=""
                  description=""
                  elevation={1}
                  onPress={() => {}}
                  disabled={false}
                  style={{}}
                  data={{}}
                  growth={0}
                  status=""
                  statusColor=""
                />
              </View>
            </View>

            {/* Bulk Operations Button */}
            {filteredProducts.length > 0 && (
              <View style={styles.bulkOperationsContainer}>
                <Button
                  mode="outlined"
                  onPress={handleBulkOperations}
                  icon="checkbox-multiple-marked"
                  style={styles.bulkOperationsButton}
                >
                  Bulk Operations ({filteredProducts.length})
                </Button>
              </View>
            )}
          </View>
        )}
        ListEmptyComponent={() => (
          <UnifiedEmptyState
            type="products"
            searchQuery={searchQuery}
            onActionPress={handleAddProduct}
            description="No products found"
            actionLabel="Add Product"
            style={{}}
            iconColor={theme.colors.primary}
          />
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchbar: {
    borderRadius: BORDER_RADIUS.round,
    height: 48,
    ...SHADOWS.sm,
  },
  categoryContainer: {
    marginBottom: SPACING.sm,
  },
  categoryChip: {
    marginRight: SPACING.xs + 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
    gap: SPACING.xs,
  },
  statCardWrapper: {
    flex: 1,
  },
  productCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  productContent: {
    padding: SPACING.lg,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  productInfo: {
    flex: 1,
  },
  productMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  stockDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addButton: {
    borderRadius: 12,
    width: 48,
    height: 48,
  },
  bulkOperationsContainer: {
    marginBottom: SPACING.lg,
  },
  bulkOperationsButton: {
    borderRadius: BORDER_RADIUS.lg,
  },
});

export default ProductsScreen;