/**
 * ScanScreen - QR/Barcode scanning functionality
 * Handles product scanning, order scanning, and general QR code scanning
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Dimensions } from 'react-native';
import { Text, <PERSON><PERSON>, Card } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import LoggingService from '../services/LoggingService';

// Try to import camera modules, but handle gracefully if not available
let Camera: any, BarCodeScanner: any;
let cameraAvailable = false;
try {
  const cameraModule = require('expo-camera');
  const barcodeScannerModule = require('expo-barcode-scanner');
  Camera = cameraModule.Camera;
  BarCodeScanner = barcodeScannerModule.BarCodeScanner;
  cameraAvailable = true;
} catch (error) {
  LoggingService.warn('Camera modules not available in Expo Go', 'SCAN', error as Error);
  Camera = null;
  BarCodeScanner = null;
  cameraAvailable = false;
}

const { width, height } = Dimensions.get('window');

interface ScanScreenProps {
  navigation: any;
}

interface ScannedData {
  type: string;
  data: string;
  name?: string;
  price?: number;
  barcode?: string;
  orderId?: string;
  customer?: string;
  status?: string;
  url?: string;
  text?: string;
}

interface BarCodeScanningResult {
  type: string;
  data: string;
}

const ScanScreen: React.FC<ScanScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [scannedData, setScannedData] = useState<ScannedData | null>(null);
  const [scanMode, setScanMode] = useState<'product' | 'order' | 'general'>('product');
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [useCamera, setUseCamera] = useState<boolean>(false);

  // Request camera permissions
  useEffect(() => {
    (async () => {
      if (!cameraAvailable || !Camera) {
        LoggingService.info('Camera not available, skipping permission request', 'SCAN');
        setHasPermission(false);
        return;
      }

      try {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
      } catch (error) {
        LoggingService.error('Camera permission error', 'SCAN', error as Error);
        setHasPermission(false);
      }
    })();
  }, []);

  // Handle barcode scan
  const handleBarCodeScanned = ({ type, data }: BarCodeScanningResult): void => {
    setIsScanning(false);
    setUseCamera(false);

    // Process the scanned data based on scan mode
    let processedData: ScannedData;

    if (scanMode === 'product') {
      // Check if it's a product barcode
      const product = state.products.find((p: any) => p.barcode === data);
      if (product) {
        processedData = {
          type: 'product',
          data: data,
          name: product.name,
          price: product.price,
          barcode: data
        };
      } else {
        processedData = {
          type: 'product',
          data: data,
          name: 'Unknown Product',
          price: 0,
          barcode: data
        };
      }
    } else if (scanMode === 'order') {
      processedData = {
        type: 'order',
        data: data,
        orderId: data,
        customer: 'Unknown Customer',
        status: 'Pending'
      };
    } else {
      processedData = {
        type: 'qr',
        data: data,
        url: data.startsWith('http') ? data : undefined,
        text: data
      };
    }

    setScannedData(processedData);
    LoggingService.info('Barcode scanned successfully', 'SCAN', { type, data });
  };

  // Start scanning
  const startScanning = (): void => {
    if (!cameraAvailable) {
      Alert.alert(
        'Camera Not Available',
        'Camera functionality is not available in Expo Go. Please use a development build or enter data manually.',
        [
          { text: 'OK' },
          { text: 'Manual Entry', onPress: () => handleManualEntry() }
        ]
      );
      return;
    }

    if (hasPermission === null) {
      Alert.alert('Permission Required', 'Camera permission is required to scan codes.');
      return;
    }

    if (hasPermission === false) {
      Alert.alert('No Permission', 'Camera permission was denied. Please enable it in settings.');
      return;
    }

    setIsScanning(true);
    setUseCamera(true);
    setScannedData(null);
  };

  // Handle manual entry
  const handleManualEntry = (): void => {
    Alert.prompt(
      'Manual Entry',
      'Enter the barcode or QR code data:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'OK',
          onPress: (text) => {
            if (text && text.trim()) {
              handleBarCodeScanned({ type: 'manual', data: text.trim() });
            }
          }
        }
      ],
      'plain-text'
    );
  };

  // Handle scanned result actions
  const handleResultAction = (): void => {
    if (!scannedData) return;

    if (scannedData.type === 'product') {
      if (scannedData.name !== 'Unknown Product') {
        Alert.alert(
          'Product Found',
          `${scannedData.name} - ৳${scannedData.price}`,
          [
            { text: 'OK' },
            { text: 'Add to Order', onPress: () => {
              // Navigate to add order with this product
              navigation.navigate('AddOrder', { preselectedProduct: scannedData });
            }}
          ]
        );
      } else {
        Alert.alert(
          'Product Not Found',
          `Barcode: ${scannedData.barcode}\nThis product is not in your inventory.`,
          [
            { text: 'OK' },
            { text: 'Add Product', onPress: () => {
              // Navigate to add product with this barcode
              navigation.navigate('AddProduct', { barcode: scannedData.barcode });
            }}
          ]
        );
      }
    } else if (scannedData.type === 'order') {
      Alert.alert(
        'Order Code',
        `Order ID: ${scannedData.orderId}`,
        [
          { text: 'OK' },
          { text: 'View Orders', onPress: () => navigation.navigate('Orders') }
        ]
      );
    } else if (scannedData.type === 'qr') {
      if (scannedData.url) {
        Alert.alert(
          'QR Code - URL',
          scannedData.url,
          [
            { text: 'OK' },
            { text: 'Open', onPress: () => {
              // In a real app, you might want to open the URL
              LoggingService.info('QR URL scanned', 'SCAN', { url: scannedData.url });
            }}
          ]
        );
      } else {
        Alert.alert('QR Code', scannedData.text || 'Unknown content');
      }
    }
  };

  // Render camera view
  const renderCamera = (): React.ReactElement | null => {
    if (!useCamera || !cameraAvailable || !Camera || !BarCodeScanner) {
      return null;
    }

    return (
      <View style={styles.cameraContainer}>
        <Camera
          style={styles.camera}
          type={Camera.Constants?.Type?.back || 'back'}
          onBarCodeScanned={isScanning ? undefined : handleBarCodeScanned}
          barCodeScannerSettings={{
            barCodeTypes: [BarCodeScanner.Constants?.BarCodeType?.qr, BarCodeScanner.Constants?.BarCodeType?.ean13],
          }}
        />
        <View style={styles.overlay}>
          <View style={styles.scanArea} />
          <Text style={[styles.scanText, { color: theme.colors.onPrimary }]}>
            Position the {scanMode} code within the frame
          </Text>
        </View>
        <Button
          mode="contained"
          onPress={() => {
            setUseCamera(false);
            setIsScanning(false);
          }}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
      </View>
    );
  };

  // Render scan modes
  const renderScanModes = (): React.ReactElement => (
    <View style={styles.modesContainer}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Scan Mode
      </Text>
      <View style={styles.modeButtons}>
        {[
          { key: 'product', label: 'Product', icon: 'barcode-scan' },
          { key: 'order', label: 'Order', icon: 'clipboard-text' },
          { key: 'general', label: 'QR Code', icon: 'qrcode-scan' }
        ].map((mode) => (
          <Button
            key={mode.key}
            mode={scanMode === mode.key ? 'contained' : 'outlined'}
            onPress={() => setScanMode(mode.key as any)}
            icon={mode.icon}
            style={styles.modeButton}
          >
            {mode.label}
          </Button>
        ))}
      </View>
    </View>
  );

  // Render scanned result
  const renderScannedResult = (): React.ReactElement | null => {
    if (!scannedData) return null;

    return (
      <Card style={[styles.resultCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
            Scanned Result
          </Text>
          
          {scannedData.type === 'product' && (
            <>
              <Text variant="bodyLarge" style={{ fontWeight: '600' }}>{scannedData.name}</Text>
              <Text variant="bodyMedium">Price: ৳{scannedData.price}</Text>
              <Text variant="bodySmall">Barcode: {scannedData.barcode}</Text>
            </>
          )}
          
          {scannedData.type === 'order' && (
            <>
              <Text variant="bodyLarge" style={{ fontWeight: '600' }}>Order: {scannedData.orderId}</Text>
              <Text variant="bodyMedium">Customer: {scannedData.customer}</Text>
              <Text variant="bodySmall">Status: {scannedData.status}</Text>
            </>
          )}
          
          {scannedData.type === 'qr' && (
            <>
              <Text variant="bodyLarge" style={{ fontWeight: '600' }}>QR Code</Text>
              <Text variant="bodyMedium">{scannedData.text}</Text>
            </>
          )}
        </Card.Content>
        <Card.Actions>
          <Button onPress={handleResultAction}>
            {scannedData.type === 'product' ? 'View Product' : 
             scannedData.type === 'order' ? 'View Order' : 'Open'}
          </Button>
          <Button onPress={() => setScannedData(null)}>Clear</Button>
        </Card.Actions>
      </Card>
    );
  };

  if (useCamera) {
    return renderCamera();
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Scan"
        subtitle="Scan barcodes and QR codes"
        showSearch={false}
      />

      <View style={styles.content}>
        {renderScanModes()}

        <View style={styles.actionContainer}>
          <Button
            mode="contained"
            onPress={startScanning}
            icon="camera"
            style={styles.scanButton}
            buttonColor={theme.colors.primary}
          >
            Start Scanning
          </Button>

          <Button
            mode="outlined"
            onPress={handleManualEntry}
            icon="keyboard"
            style={styles.manualButton}
          >
            Manual Entry
          </Button>
        </View>

        {renderScannedResult()}

        {/* Instructions */}
        <Card style={[styles.instructionsCard, { backgroundColor: theme.colors.surfaceVariant }]}>
          <Card.Content>
            <Text variant="titleSmall" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
              Instructions
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              • Product: Scan product barcodes to view details or add to orders{'\n'}
              • Order: Scan order QR codes to view order information{'\n'}
              • QR Code: Scan general QR codes for various content{'\n'}
              • Use Manual Entry if camera is not available
            </Text>
          </Card.Content>
        </Card>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  modesContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12,
  },
  modeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
  },
  actionContainer: {
    gap: 12,
    marginBottom: 24,
  },
  scanButton: {
    paddingVertical: 8,
  },
  manualButton: {
    paddingVertical: 8,
  },
  resultCard: {
    marginBottom: 24,
  },
  instructionsCard: {
    marginTop: 'auto',
  },
  cameraContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  scanText: {
    marginTop: 20,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    position: 'absolute',
    bottom: 50,
    left: 20,
    right: 20,
  },
});

export default ScanScreen;