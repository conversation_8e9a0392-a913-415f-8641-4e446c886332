import React, { useEffect, useState, useCallback } from 'react';
import { View, FlatList, StyleSheet, Alert, ListRenderItem } from 'react-native';
import { Text, IconButton, Surface, Button } from 'react-native-paper';
import { NotificationService, useNotifications, Notification } from '../services/notificationService';
import CommonHeader from '../components/CommonHeader';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';
import LoggingService from '../services/LoggingService';

interface NotificationsScreenProps {
  navigation: any;
}

const NotificationsScreen: React.FC<NotificationsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { notifications, markAsRead, deleteNotification, clearAll } = useNotifications();

  const handleNotificationPress = useCallback((notification: Notification): void => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    if (notification.data && typeof notification.data.action === 'function') {
      try {
        notification.data.action();
      } catch (error) {
        LoggingService.error('Error executing notification action', 'NOTIFICATION', error as Error);
      }
    }
  }, [markAsRead]);

  const handleDelete = useCallback((id: string): void => {
    Alert.alert('Delete Notification', 'Are you sure you want to delete this notification?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Delete', style: 'destructive', onPress: () => deleteNotification(id) },
    ]);
  }, [deleteNotification]);

  const renderItem: ListRenderItem<Notification> = ({ item }) => (
    <Surface 
      style={[
        styles.notificationCard, 
        { 
          backgroundColor: item.read ? theme.colors.surface : theme.colors.primaryContainer 
        }
      ]} 
      elevation={1}
    >
      <View style={styles.notificationContent}>
        <View style={{ flex: 1 }}>
          <Text variant="titleMedium" style={{ fontWeight: item.read ? '400' : '700' }}>
            {item.title}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            {item.message}
          </Text>
          <Text variant="bodySmall" style={{ marginTop: 4, color: theme.colors.onSurfaceVariant }}>
            {new Date(item.timestamp).toLocaleString()}
          </Text>
        </View>
        <View style={styles.actions}>
          {!item.read && (
            <IconButton icon="check" size={20} onPress={() => markAsRead(item.id)} />
          )}
          <IconButton icon="delete" size={20} onPress={() => handleDelete(item.id)} />
        </View>
      </View>
      {item.data && item.data.action && (
        <Button 
          mode="text" 
          onPress={() => handleNotificationPress(item)} 
          style={{ alignSelf: 'flex-start' }}
        >
          {item.data.actionText || 'View'}
        </Button>
      )}
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}> 
      <CommonHeader
        title="Notifications"
        subtitle="Your recent notifications"
        showSearch={false}
        showNotifications={false}
      />
      <FlatList
        data={notifications}
        keyExtractor={(item: Notification) => item.id}
        renderItem={renderItem}
        contentContainerStyle={[styles.listContent, { padding: SPACING.md }]}
        ListEmptyComponent={
          <Text style={{ 
            textAlign: 'center', 
            marginTop: SPACING.xl, 
            color: theme.colors.onSurfaceVariant,
            fontSize: TYPOGRAPHY.fontSize.md,
            lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.md
          }}>
            No notifications
          </Text>
        }
      />
      {notifications.length > 0 && (
        <Button 
          mode="outlined" 
          onPress={clearAll} 
          style={[styles.clearAllBtn, { margin: SPACING.md }]}
        >
          Clear All
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { 
    flex: 1 
  },
  listContent: {},
  notificationCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  notificationContent: { 
    flexDirection: 'row', 
    alignItems: 'center' 
  },
  actions: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    marginLeft: SPACING.sm 
  },
  clearAllBtn: {},
});

export default NotificationsScreen;