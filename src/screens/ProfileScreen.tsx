import React, { useState, useRef } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoggingService from '../services/LoggingService';
import {
  Card,
  Text,
  Button,
  Surface,
  Avatar,
  List,
  Divider,
  Switch,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';
import EditProfileBottomSheet from '../components/EditProfileBottomSheet';
import PaymentMethodsModal from '../components/PaymentMethodsModal';
import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import ImagePicker from '../components/ImagePicker';
import SecuritySettingsBottomSheet from '../components/SecuritySettingsBottomSheet';
import UnifiedSwitch from '../components/UnifiedSwitch';
import ProfileSectionList from '../components/ProfileSectionList';

interface ProfileItem {
  icon: string;
  label: string;
  onPress: () => void;
  rightComponent?: React.ReactNode;
}

const MyProfileScreen: React.FC = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Bottom sheet refs
  const editProfileBottomSheetRef = useRef<any>(null);
  const securityBottomSheetRef = useRef<any>(null);

  // Modal states
  const [paymentMethodsModalVisible, setPaymentMethodsModalVisible] = useState<boolean>(false);

  // Profile-related settings state
  const [notifications, setNotifications] = useState<boolean>(state.settings.notifications || true);
  const [autoBackup, setAutoBackup] = useState<boolean>(state.settings.autoBackup || true);

  const handleProfileSave = (profileData: any): void => {
    actions.updateSettings(profileData);
    Alert.alert('Success', 'Profile updated successfully!');
  };

  const handlePaymentMethodsSave = (paymentMethods: any): void => {
    actions.updateSettings({ paymentMethods });
    Alert.alert('Success', 'Payment methods updated successfully!');
  };

  const handleSettingChange = (key: string, value: boolean): void => {
    const newSettings = { [key]: value };
    actions.updateSettings(newSettings);
    if (key === 'notifications') setNotifications(value);
    if (key === 'autoBackup') setAutoBackup(value);
  };

  // Add a logout handler
  const handleLogout = async (): Promise<void> => {
    try {
      Alert.alert(
        'Confirm Logout',
        'Are you sure you want to logout? This will clear your session data.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Logout',
            style: 'destructive',
            onPress: async () => {
              try {
                LoggingService.info('User logout initiated', 'AUTH');
                
                // Clear any cached authentication data
                await AsyncStorage.removeItem('user_session');
                await AsyncStorage.removeItem('auth_token');
                
                // Clear sensitive user preferences if needed
                // Note: We keep app data (products, orders, etc.) as this is a local app
                
                LoggingService.info('User logout completed successfully', 'AUTH');
                
                // Show success message
                Alert.alert(
                  'Logged Out',
                  'You have been successfully logged out.',
                  [{ text: 'OK' }]
                );
                
                // In a real app with authentication, you would navigate to login screen
                // For now, we just show the confirmation
                
              } catch (error) {
                LoggingService.error('Logout failed', 'AUTH', error as Error);
                Alert.alert(
                  'Logout Error',
                  'Failed to logout completely. Please try again.',
                  [{ text: 'OK' }]
                );
              }
            },
          },
        ]
      );
    } catch (error) {
      LoggingService.error('Logout dialog error', 'AUTH', error as Error);
      Alert.alert('Error', 'Unable to process logout request.');
    }
  };

  const ProfileHeader: React.FC = () => (
    <Surface style={[styles.profileCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={styles.avatarContainer}>
              {(state.settings as any).profileImage ? (
                <Avatar.Image
                  size={56}
                  source={{ uri: (state.settings as any).profileImage }}
                />
              ) : (
                <Avatar.Text
                  size={56}
                  label={state.settings.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
                />
              )}
            </View>
            <View style={styles.profileInfo}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Text style={{
                  fontWeight: '700',
                  color: theme.colors.onSurface,
                  fontSize: TYPOGRAPHY.fontSize.lg,
                  lineHeight: TYPOGRAPHY.lineHeight.tight * TYPOGRAPHY.fontSize.lg
                }}>
                  {state.settings.storeName}
                </Text>
                <View style={{
                  backgroundColor: theme.colors.primary + '22',
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                  marginLeft: 4,
                }}>
                  <Text style={{ color: theme.colors.primary, fontWeight: '600', fontSize: 12 }}>
                    {((state.settings as any).role || 'Owner').toUpperCase()}
                  </Text>
                </View>
              </View>
              <Text style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: 2,
                fontSize: TYPOGRAPHY.fontSize.sm,
                fontWeight: TYPOGRAPHY.fontWeight.normal
              }}>
                {state.settings.phone}
              </Text>
            </View>
          </View>
          <Button
            mode="outlined"
            style={[styles.editButton, { minWidth: 24, paddingHorizontal: 4, height: 32 }]}
            labelStyle={{ fontSize: 12, paddingVertical: 0, paddingHorizontal: 0 }}
            onPress={() => editProfileBottomSheetRef.current?.expand()}
            icon="pencil"
            compact
          >
            Edit
          </Button>
        </View>
      </View>
    </Surface>
  );

  // Define all section items for ProfileSectionList
  const preferencesItems: ProfileItem[] = [
    {
      icon: 'bell',
      label: 'Notifications',
      onPress: () => handleSettingChange('notifications', !notifications),
      rightComponent: (
        <UnifiedSwitch
          value={notifications}
          onValueChange={(value: boolean) => handleSettingChange('notifications', value)}
          style={{}}
        />
      ),
    },
    {
      icon: 'backup-restore',
      label: 'Auto Backup',
      onPress: () => handleSettingChange('autoBackup', !autoBackup),
      rightComponent: (
        <UnifiedSwitch
          value={autoBackup}
          onValueChange={(value: boolean) => handleSettingChange('autoBackup', value)}
          style={{}}
        />
      ),
    },
    {
      icon: 'shield-check',
      label: 'Security',
      onPress: () => securityBottomSheetRef.current?.expand(),
    },
  ];

  const appSettingsItems: ProfileItem[] = [
    {
      icon: 'theme-light-dark',
      label: 'Dark Mode',
      rightComponent: (
        <UnifiedSwitch
          value={isDarkMode}
          onValueChange={toggleTheme}
          style={{}}
        />
      ),
      onPress: toggleTheme,
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="My Profile"
        subtitle="Personal & business settings"
        showSearch={false}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            LoggingService.error('Failed to navigate to NotificationsScreen', 'NAVIGATION', error as Error);
          }
        }}
        onProfilePress={() => navigation.goBack()}
      />
      <ScrollView 
        style={{ flex: 1, backgroundColor: theme.colors.background }} 
        contentContainerStyle={{ padding: 0, margin: 0 }} 
        showsVerticalScrollIndicator={false}
      >
        <ProfileHeader />
        
        {/* Preferences Section */}
        <ProfileSectionList title="Profile Preferences" items={preferencesItems} />
        
        {/* App Settings Section */}
        <ProfileSectionList title="App Preferences" items={appSettingsItems} />
        
        {/* Logout Section */}
        <ProfileSectionList
          title=""
          items={[]}
          footer={
            <Button
              mode="contained"
              icon="logout"
              onPress={handleLogout}
              style={{ width: '90%', borderRadius: 8 }}
              contentStyle={{ paddingVertical: 6 }}
              buttonColor={theme.colors.error}
              textColor={theme.colors.onError}
            >
              Logout
            </Button>
          }
        />
      </ScrollView>
      
      {/* Bottom Sheets and Modals */}
      {React.createElement(EditProfileBottomSheet as any, {
        ref: editProfileBottomSheetRef,
        profile: state.settings,
        onSave: handleProfileSave,
        onClose: () => {}
      })}
      
      {React.createElement(SecuritySettingsBottomSheet as any, {
        ref: securityBottomSheetRef
      })}
      
      {React.createElement(PaymentMethodsModal as any, {
        visible: paymentMethodsModalVisible,
        onDismiss: () => setPaymentMethodsModalVisible(false),
        onSave: handlePaymentMethodsSave,
        paymentMethods: (state.settings as any).paymentMethods
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  profileCard: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  profileContent: {
    padding: SPACING.lg,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  profileMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    padding: SPACING.xs,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md,
    position: 'relative',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  profileInfo: {
    flex: 1,
  },
  contactInfo: {
    gap: SPACING.xs,
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  editButton: {
    minWidth: 80,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  sectionCard: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  listItem: {
    paddingVertical: SPACING.xs,
  },
  sectionContainer: {
    marginBottom: SPACING.lg,
  },
  settingItem: {
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  footer: {
    marginTop: SPACING.lg,
    paddingVertical: SPACING.md,
  },
});

export default MyProfileScreen;