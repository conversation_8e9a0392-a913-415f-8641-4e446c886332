/**
 * useUnifiedNavigation - Unified navigation hook
 * Provides all navigation methods and state in one convenient hook
 */

import { useCallback } from 'react';
import { useNavigation as useRNNavigation } from '@react-navigation/native';
import { useNavigationContext } from '../context/NavigationContext';
import navigationService from '../services/NavigationService';

export interface UseUnifiedNavigationReturn {
  // Core navigation
  navigate: (name: string, params?: any) => void;
  goBack: () => void;
  reset: (state: any) => void;

  // Tab navigation
  navigateToTab: (tabName: string) => void;
  switchTab: (tabName: string) => void;
  goToDashboard: () => void;
  goToScan: () => void;
  goToOrders: () => void;

  // Modal navigation
  openModal: (modalName: string, params?: any) => void;
  closeModal: () => void;
  goToProducts: (mode?: string, productId?: string | null) => void;

  // Settings navigation
  openSettings: (settingsScreen?: string) => void;
  openProfile: () => void;

  // Quick actions
  openQuickActions: () => void;
  closeQuickActions: () => void;
  executeQuickAction: (action: string, params?: any) => void;

  // Search
  activateSearch: () => void;
  deactivateSearch: () => void;
  updateSearchQuery: (query: string) => void;

  // Notifications
  openNotifications: () => void;
  closeNotifications: () => void;

  // Profile menu
  openProfileMenu: () => void;
  closeProfileMenu: () => void;

  // Utilities
  getCurrentRoute: () => string | null;
  getCurrentTab: () => string;
  isCurrentRoute: (routeName: string) => boolean;
  isCurrentTab: (tabName: string) => boolean;
  canGoBack: () => boolean;
  getNavigationHistory: () => any[];
  getBreadcrumbs: () => any[];
  getNavigationAnalytics: () => any;
  buildDeepLink: (routeName: string, params?: any) => string;

  // State
  currentRoute: string;
  currentTab: string;
  isModalOpen: boolean;
  isQuickActionsVisible: boolean;
  isSearchActive: boolean;
  searchQuery: string;
  isNotificationsVisible: boolean;
  notificationCount: number;
  isProfileMenuVisible: boolean;
  isNavigationReady: boolean;
  breadcrumbs: any[];

  // Raw navigation objects (for advanced use)
  rnNavigation: any;
  navigationContext: any;
  navigationService: any;
}

export const useUnifiedNavigation = (): UseUnifiedNavigationReturn => {
  const rnNavigation = useRNNavigation();
  const navigationContext = useNavigationContext();

  // Core navigation methods
  const navigate = useCallback((name: string, params?: any) => {
    navigationService.navigate(name, params);
    navigationContext.actions.setCurrentRoute(name);
  }, [navigationContext.actions]);

  const goBack = useCallback(() => {
    navigationService.goBack();
  }, []);

  const reset = useCallback((state: any) => {
    navigationService.reset(state);
    navigationContext.actions.resetNavigation();
  }, [navigationContext.actions]);

  // Tab navigation
  const navigateToTab = useCallback((tabName: string) => {
    navigationContext.actions.navigateToTab(tabName);
  }, [navigationContext.actions]);

  const switchTab = useCallback((tabName: string) => {
    navigateToTab(tabName);
  }, [navigateToTab]);

  // Modal navigation
  const openModal = useCallback((modalName: string, params?: any) => {
    navigationContext.actions.openModal(modalName, params);
  }, [navigationContext.actions]);

  const closeModal = useCallback(() => {
    navigationContext.actions.closeModal();
  }, [navigationContext.actions]);

  // Settings navigation
  const openSettings = useCallback((settingsScreen?: string) => {
    navigationContext.actions.openSettings(settingsScreen);
  }, [navigationContext.actions]);

  const openProfile = useCallback(() => {
    openSettings('Profile');
  }, [openSettings]);

  // Quick actions
  const openQuickActions = useCallback(() => {
    navigationContext.actions.toggleQuickActions(true);
  }, [navigationContext.actions]);

  const closeQuickActions = useCallback(() => {
    navigationContext.actions.toggleQuickActions(false);
  }, [navigationContext.actions]);

  const executeQuickAction = useCallback((action: string, params?: any) => {
    navigationContext.actions.openQuickAction(action, params);
  }, [navigationContext.actions]);

  // Search functionality
  const activateSearch = useCallback(() => {
    navigationContext.actions.setSearchActive(true);
  }, [navigationContext.actions]);

  const deactivateSearch = useCallback(() => {
    navigationContext.actions.setSearchActive(false);
  }, [navigationContext.actions]);

  const updateSearchQuery = useCallback((query: string) => {
    navigationContext.actions.setSearchQuery(query);
  }, [navigationContext.actions]);

  // Notifications
  const openNotifications = useCallback(() => {
    navigationContext.actions.toggleNotifications(true);
  }, [navigationContext.actions]);

  const closeNotifications = useCallback(() => {
    navigationContext.actions.toggleNotifications(false);
  }, [navigationContext.actions]);

  // Profile menu
  const openProfileMenu = useCallback(() => {
    navigationContext.actions.toggleProfile(true);
  }, [navigationContext.actions]);

  const closeProfileMenu = useCallback(() => {
    navigationContext.actions.toggleProfile(false);
  }, [navigationContext.actions]);

  // Specific screen navigation
  const goToDashboard = useCallback(() => {
    navigateToTab('Dashboard');
  }, [navigateToTab]);

  const goToScan = useCallback(() => {
    navigateToTab('Scan');
  }, [navigateToTab]);

  const goToOrders = useCallback(() => {
    navigateToTab('Orders');
  }, [navigateToTab]);

  const goToProducts = useCallback((mode: string = 'view', productId: string | null = null) => {
    openModal('Products', { mode, productId });
  }, [openModal]);

  // Utility methods
  const getCurrentRoute = useCallback((): string | null => {
    return navigationService.getCurrentRouteName();
  }, []);

  const getCurrentTab = useCallback((): string => {
    return navigationContext.currentTab;
  }, [navigationContext.currentTab]);

  const isCurrentRoute = useCallback((routeName: string): boolean => {
    return navigationContext.currentRoute === routeName;
  }, [navigationContext.currentRoute]);

  const isCurrentTab = useCallback((tabName: string): boolean => {
    return navigationContext.currentTab === tabName;
  }, [navigationContext.currentTab]);

  const canGoBack = useCallback((): boolean => {
    return rnNavigation.canGoBack();
  }, [rnNavigation]);

  const getNavigationHistory = useCallback((): any[] => {
    return navigationService.getRouteHistory();
  }, []);

  const getBreadcrumbs = useCallback((): any[] => {
    return navigationContext.breadcrumbs;
  }, [navigationContext.breadcrumbs]);

  // Analytics
  const getNavigationAnalytics = useCallback((): any => {
    return navigationService.getNavigationAnalytics();
  }, []);

  // Deep linking
  const buildDeepLink = useCallback((routeName: string, params?: any): string => {
    return navigationService.buildDeepLink(routeName, params);
  }, []);

  // State getters
  const isModalOpen = navigationContext.isModalOpen;
  const isQuickActionsVisible = navigationContext.quickActionsVisible;
  const isSearchActive = navigationContext.searchActive;
  const searchQuery = navigationContext.searchQuery;
  const isNotificationsVisible = navigationContext.notifications.visible;
  const notificationCount = navigationContext.notifications.count;
  const isProfileMenuVisible = navigationContext.user.profileVisible;
  const isNavigationReady = navigationContext.navigationReady;

  return {
    // Core navigation
    navigate,
    goBack,
    reset,

    // Tab navigation
    navigateToTab,
    switchTab,
    goToDashboard,
    goToScan,
    goToOrders,

    // Modal navigation
    openModal,
    closeModal,
    goToProducts,

    // Settings navigation
    openSettings,
    openProfile,

    // Quick actions
    openQuickActions,
    closeQuickActions,
    executeQuickAction,

    // Search
    activateSearch,
    deactivateSearch,
    updateSearchQuery,

    // Notifications
    openNotifications,
    closeNotifications,

    // Profile menu
    openProfileMenu,
    closeProfileMenu,

    // Utilities
    getCurrentRoute,
    getCurrentTab,
    isCurrentRoute,
    isCurrentTab,
    canGoBack,
    getNavigationHistory,
    getBreadcrumbs,
    getNavigationAnalytics,
    buildDeepLink,

    // State
    currentRoute: navigationContext.currentRoute,
    currentTab: navigationContext.currentTab,
    isModalOpen,
    isQuickActionsVisible,
    isSearchActive,
    searchQuery,
    isNotificationsVisible,
    notificationCount,
    isProfileMenuVisible,
    isNavigationReady,
    breadcrumbs: navigationContext.breadcrumbs,

    // Raw navigation objects (for advanced use)
    rnNavigation,
    navigationContext,
    navigationService,
  };
};

export default useUnifiedNavigation;