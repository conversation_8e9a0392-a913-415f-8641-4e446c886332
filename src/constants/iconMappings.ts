/**
 * Standardized Icon Mappings
 * Ensures consistent icon usage across the entire app
 */

// Action Icons - Consistent across all screens
export const ACTION_ICONS = {
  // CRUD Operations
  add: 'plus',
  edit: 'pencil',
  delete: 'delete',
  save: 'content-save',
  cancel: 'close',
  confirm: 'check',
  
  // Navigation
  back: 'arrow-left',
  forward: 'arrow-right',
  up: 'chevron-up',
  down: 'chevron-down',
  left: 'chevron-left',
  right: 'chevron-right',
  
  // UI Actions
  search: 'magnify',
  filter: 'filter',
  sort: 'sort',
  refresh: 'refresh',
  settings: 'cog',
  menu: 'dots-vertical',
  close: 'close',
  expand: 'chevron-down',
  collapse: 'chevron-up',
  
  // File Operations
  export: 'export',
  import: 'import',
  download: 'download',
  upload: 'upload',
  share: 'share',
  
  // Media
  camera: 'camera',
  image: 'image',
  video: 'video',
} as const;

// Status Icons - Consistent status representations
export const STATUS_ICONS = {
  // Order Status
  pending: 'clock-outline',
  processing: 'cog-outline',
  ready: 'check-circle-outline',
  completed: 'check-all',
  cancelled: 'close-circle-outline',
  
  // General Status
  success: 'check-circle',
  error: 'alert-circle',
  warning: 'alert',
  info: 'information',
  
  // Availability
  active: 'circle',
  inactive: 'circle-outline',
  online: 'circle',
  offline: 'circle-outline',
  
  // Stock Status
  inStock: 'check-circle',
  lowStock: 'alert',
  outOfStock: 'close-circle',
} as const;

// Business Icons - Bakery and business specific
export const BUSINESS_ICONS = {
  // Core Business
  bakery: 'food-croissant',
  product: 'package-variant',
  order: 'clipboard-list',
  customer: 'account',
  
  // Financial
  sales: 'chart-line',
  revenue: 'cash-register',
  expense: 'receipt',
  profit: 'trending-up',
  loss: 'trending-down',
  
  // Inventory
  stock: 'package',
  category: 'tag',
  barcode: 'barcode-scan',
  
  // Analytics
  analytics: 'chart-pie',
  reports: 'file-chart',
  dashboard: 'view-dashboard',
  
  // Operations
  kitchen: 'chef-hat',
  delivery: 'truck-delivery',
  pickup: 'hand-extended',
} as const;

// Navigation Icons - Bottom nav and main navigation
export const NAVIGATION_ICONS = {
  // Bottom Navigation
  dashboard: 'view-dashboard-outline',
  scan: 'qrcode-scan',
  plus: 'plus',
  orders: 'clipboard-list-outline',
  settings: 'cog-outline',
  
  // Main Navigation
  products: 'package-variant-closed',
  customers: 'account-group',
  financial: 'cash-register',
  reports: 'chart-box',
  profile: 'account-circle',
  
  // Secondary Navigation
  help: 'help-circle-outline',
  about: 'information-outline',
  contact: 'phone',
  faq: 'frequently-asked-questions',
} as const;

// UI Component Icons - For specific UI elements
export const UI_ICONS = {
  // Form Elements
  dropdown: 'chevron-down',
  calendar: 'calendar',
  time: 'clock-outline',
  
  // Notifications
  notification: 'bell',
  notificationActive: 'bell-ring',
  
  // User Interface
  visibility: 'eye',
  visibilityOff: 'eye-off',
  copy: 'content-copy',
  paste: 'content-paste',
  
  // Social/Communication
  email: 'email',
  phone: 'phone',
  whatsapp: 'whatsapp',
  
  // Security
  lock: 'lock',
  unlock: 'lock-open',
  security: 'shield-check',
} as const;

// Type definitions
export type ActionIconKey = keyof typeof ACTION_ICONS;
export type StatusIconKey = keyof typeof STATUS_ICONS;
export type BusinessIconKey = keyof typeof BUSINESS_ICONS;
export type NavigationIconKey = keyof typeof NAVIGATION_ICONS;
export type UIIconKey = keyof typeof UI_ICONS;

export type IconCategory = 'action' | 'status' | 'business' | 'navigation' | 'ui';

// Get standardized icon for action
export const getActionIcon = (action: ActionIconKey): string => {
  return ACTION_ICONS[action] || 'help-circle-outline';
};

// Get standardized icon for status
export const getStatusIcon = (status: StatusIconKey): string => {
  return STATUS_ICONS[status] || 'information';
};

// Get standardized icon for business entity
export const getBusinessIcon = (entity: BusinessIconKey): string => {
  return BUSINESS_ICONS[entity] || 'package-variant';
};

// Get standardized icon for navigation
export const getNavigationIcon = (route: NavigationIconKey): string => {
  return NAVIGATION_ICONS[route] || 'view-dashboard-outline';
};

// Get standardized icon for UI component
export const getUIIcon = (component: UIIconKey): string => {
  return UI_ICONS[component] || 'circle-outline';
};

// Comprehensive icon getter with fallback
export const getStandardIcon = (category: IconCategory, name: string): string => {
  const iconMaps = {
    action: ACTION_ICONS,
    status: STATUS_ICONS,
    business: BUSINESS_ICONS,
    navigation: NAVIGATION_ICONS,
    ui: UI_ICONS,
  };
  
  const iconMap = iconMaps[category];
  if (iconMap && (iconMap as any)[name]) {
    return (iconMap as any)[name];
  }
  
  // Fallback to searching all categories
  for (const map of Object.values(iconMaps)) {
    if ((map as any)[name]) {
      return (map as any)[name];
    }
  }
  
  // Ultimate fallback
  return 'help-circle-outline';
};

// Icon validation - check if icon exists in our standard set
export const isStandardIcon = (iconName: string): boolean => {
  const allIcons = [
    ...Object.values(ACTION_ICONS),
    ...Object.values(STATUS_ICONS),
    ...Object.values(BUSINESS_ICONS),
    ...Object.values(NAVIGATION_ICONS),
    ...Object.values(UI_ICONS),
  ];
  
  return allIcons.includes(iconName as any);
};

// Get all icons in a category
export const getIconsByCategory = (category: IconCategory): Record<string, string> => {
  const iconMaps = {
    action: ACTION_ICONS,
    status: STATUS_ICONS,
    business: BUSINESS_ICONS,
    navigation: NAVIGATION_ICONS,
    ui: UI_ICONS,
  };
  
  return iconMaps[category] || {};
};