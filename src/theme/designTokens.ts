/**
 * Design Tokens - Centralized design system values
 * All spacing, sizing, typography, and other design values should be defined here
 */

// Spacing System (8pt grid)
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
  xxxxxl: 48,
  xxxxxxl: 64,
} as const;

// Border Radius System
export const BORDER_RADIUS = {
  xs: 4,
  sm: 6,
  md: 8,
  lg: 10,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  round: 25,
  circle: 50,
} as const;

// Typography System
export const TYPOGRAPHY = {
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    xxxxl: 28,
    xxxxxl: 32,
    xxxxxxl: 36,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
} as const;

// Shadow System
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.16,
    shadowRadius: 16,
    elevation: 8,
  },
} as const;

// Component Sizes
export const COMPONENT_SIZES = {
  button: {
    sm: { height: 32, paddingHorizontal: 12 },
    md: { height: 40, paddingHorizontal: 16 },
    lg: { height: 48, paddingHorizontal: 20 },
  },
  input: {
    sm: { height: 32 },
    md: { height: 40 },
    lg: { height: 48 },
  },
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 28,
    xxl: 32,
  },
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
    xxl: 64,
  },
} as const;

// Layout Constants
export const LAYOUT = {
  headerHeight: 56,
  tabBarHeight: 60,
  bottomSheetHeaderHeight: 60,
  listItemHeight: 56,
  cardMinHeight: 80,
  screenPadding: SPACING.lg,
  sectionSpacing: SPACING.xl,
} as const;

// Animation Durations
export const ANIMATIONS = {
  fast: 150,
  normal: 250,
  slow: 350,
  verySlow: 500,
} as const;

// Opacity Values
export const OPACITY = {
  disabled: 0.4,
  pressed: 0.7,
  overlay: 0.5,
  subtle: 0.6,
  medium: 0.8,
  high: 0.9,
} as const;

// Z-Index Values
export const Z_INDEX = {
  background: -1,
  normal: 0,
  elevated: 1,
  sticky: 10,
  overlay: 100,
  modal: 1000,
  popover: 1100,
  tooltip: 1200,
  notification: 1300,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
} as const;

// Common Styles
export const COMMON_STYLES = {
  flexCenter: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  flexBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  absoluteFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
} as const;

// Helper functions
export const getBorderColor = (theme: any): string => {
  return theme.colors.outline;
};

export const getThemedShadow = (theme: any, shadowKey: keyof typeof SHADOWS = 'md') => {
  const shadow = SHADOWS[shadowKey];
  return {
    ...shadow,
    shadowColor: theme.colors.onSurface,
  };
};

// Type definitions
export type SpacingKey = keyof typeof SPACING;
export type BorderRadiusKey = keyof typeof BORDER_RADIUS;
export type TypographyFontSizeKey = keyof typeof TYPOGRAPHY.fontSize;
export type TypographyFontWeightKey = keyof typeof TYPOGRAPHY.fontWeight;
export type ShadowKey = keyof typeof SHADOWS;
export type ComponentSizeKey = keyof typeof COMPONENT_SIZES;
export type IconSizeKey = keyof typeof COMPONENT_SIZES.icon;
export type AvatarSizeKey = keyof typeof COMPONENT_SIZES.avatar;