// Core Entity Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  barcode?: string;
  image?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  status: OrderStatus;
  orderType: OrderType;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  notes?: string;
  image?: string | null;
  items?: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id?: string;
  orderId?: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

// Enum Types
export type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';
export type ProductCategory = 'Cakes' | 'Pastries' | 'Bread' | 'Cookies' | 'Beverages' | 'Desserts' | 'Seasonal';

// UI Component Types
export interface StatCard {
  key: string;
  title: string;
  value: string | number;
  icon: string;
  color?: string;
  iconColor?: string;
  elevation?: number;
  onPress?: () => void;
  props?: Record<string, any>;
}

export interface StatCardGroupProps {
  title?: string;
  cards: StatCard[];
  columns?: number;
  showTitle?: boolean;
  titleStyle?: any;
  containerStyle?: any;
  onCardPress?: (card: StatCard, index: number) => void;
}

export interface UnifiedInfoCardProps {
  type: 'stat' | 'info' | 'action';
  title: string;
  subtitle?: string;
  value?: string | number;
  icon?: string;
  iconColor?: string;
  color?: string;
  elevation?: number;
  onPress?: () => void;
  style?: Record<string, any>;
  children?: React.ReactNode;
}

// Service Response Types
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  counts?: {
    products: number;
    customers: number;
    orders: number;
  };
}

// Database Types
export interface DatabaseConfig {
  name: string;
  version: number;
  tables: string[];
}

export interface SQLiteResult {
  insertId?: number;
  rowsAffected: number;
  rows: {
    length: number;
    item: (index: number) => any;
    _array: any[];
  };
}

// Context Types
export interface DataContextType {
  products: Product[];
  orders: Order[];
  customers: Customer[];
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateOrder: (id: string, order: Partial<Order>) => Promise<void>;
  deleteOrder: (id: string) => Promise<void>;
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
}

// Analytics Types
export interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  topProducts: Product[];
  recentOrders: Order[];
  monthlyRevenue: number[];
  dailyOrders: number[];
}

// Search Types
export interface SearchResult {
  type: 'product' | 'order' | 'customer';
  id: string;
  title: string;
  subtitle: string;
  data: Product | Order | Customer;
}

// Form Types
export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: ProductCategory;
  stock: string;
  image?: string;
}

export interface OrderFormData {
  customerName: string;
  email: string;
  phone: string;
  orderType: OrderType;
  notes?: string;
  image?: string;
  items: OrderItem[];
}

export interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  ProductForm: { product?: Product };
  OrderForm: { order?: Order };
  AddOrder: { order?: Order };
  CustomerForm: { customer?: Customer };
  ProductDetails: { productId: string };
  OrderDetails: { orderId: string };
  CustomerDetails: { customerId: string };
  Search: { query?: string };
  Analytics: undefined;
  Reports: undefined;
  Settings: undefined;
  Profile: undefined;
};

// Screen-specific prop types
export interface AddOrderScreenProps {
  route?: {
    params?: {
      order?: Order;
    };
  };
}

// Component prop types
export interface UnifiedTextInputProps {
  label: string;
  value: string;
  onChangeText: (value: string) => void;
  type?: string;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  validate?: (value: string) => string;
  error?: string;
  onBlur?: (e: any) => void;
  style?: any;
  rightAffix?: string;
  multiline?: boolean;
  numberOfLines?: number;
  keyboardType?: string;
  [key: string]: any;
}

export interface FormField {
  key: string;
  label: string;
  type: string;
  value: string;
  onChange: (val: string) => void;
  required?: boolean;
  validation?: (val: string) => string;
  multiline?: boolean;
  inputProps?: any;
  options?: Array<{ value: string; label: string }>;
  disabled?: boolean;
  rightAffix?: string;
  leftIcon?: string;
}

export interface UnifiedFormSectionProps {
  fields?: FormField[];
  errors?: Record<string, string>;
  onFieldError?: (key: string, error: string) => void;
  sectionTitle?: string;
  style?: any;
}

export interface ImagePickerProps {
  onImageSelected: (imageUri: string | null) => void;
  currentImage?: string | null;
  placeholder?: string;
  style?: any;
}

export interface UnifiedSwitchProps {
  value: any;
  onValueChange: any;
  disabled?: boolean;
  style: any;
}

export interface ProfileItem {
  icon: string;
  label: string;
  onPress: () => void;
  rightComponent?: React.ReactNode;
}

export interface ProfileSectionListProps {
  title?: string;
  items?: ProfileItem[];
  footer?: React.ReactNode;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Performance Types
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  errorRate: number;
  startupTime: number;
  fps: number;
  navigationTime: number;
}

// Cache Types
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
}
