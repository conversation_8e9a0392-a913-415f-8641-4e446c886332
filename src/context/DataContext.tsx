import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MigrationService from '../services/MigrationService';
import PerformanceOptimizer from '../services/PerformanceOptimizer';
import HealthMonitor from '../services/HealthMonitor';
import LoggingService from '../services/LoggingService';
import { Product, Order, Customer } from '../types';

// Settings interfaces
interface StoreHours {
  open: string;
  close: string;
  closed: boolean;
}

interface PaymentMethod {
  enabled: boolean;
  processingFee: number;
}

interface Settings {
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  taxRate: number;
  currency: string;
  notifications: boolean;
  darkMode: boolean;
  autoBackup: boolean;
  storeHours: {
    monday: StoreHours;
    tuesday: StoreHours;
    wednesday: StoreHours;
    thursday: StoreHours;
    friday: StoreHours;
    saturday: StoreHours;
    sunday: StoreHours;
  };
  paymentMethods: {
    cash: PaymentMethod;
    card: PaymentMethod;
    digitalWallet: PaymentMethod;
    bankTransfer: PaymentMethod;
    giftCard: PaymentMethod;
  };
}

// State interface
interface DataState {
  products: Product[];
  orders: Order[];
  customers: Customer[];
  settings: Settings;
  nextProductId: number;
  nextOrderId: number;
  nextCustomerId: number;
  isDataLoaded: boolean;
}

// Action types
enum ActionTypes {
  // Products
  ADD_PRODUCT = 'ADD_PRODUCT',
  UPDATE_PRODUCT = 'UPDATE_PRODUCT',
  DELETE_PRODUCT = 'DELETE_PRODUCT',
  UPDATE_STOCK = 'UPDATE_STOCK',

  // Orders
  ADD_ORDER = 'ADD_ORDER',
  UPDATE_ORDER = 'UPDATE_ORDER',
  DELETE_ORDER = 'DELETE_ORDER',
  UPDATE_ORDER_STATUS = 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER = 'ADD_CUSTOMER',
  UPDATE_CUSTOMER = 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER = 'DELETE_CUSTOMER',

  // Settings
  UPDATE_SETTINGS = 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA = 'LOAD_DATA',
  SET_DATA_LOADED = 'SET_DATA_LOADED',
  CLEAR_DATA = 'CLEAR_DATA',
}

// Action interfaces
interface ProductAction {
  type: ActionTypes.ADD_PRODUCT | ActionTypes.UPDATE_PRODUCT;
  payload: Product;
}

interface DeleteProductAction {
  type: ActionTypes.DELETE_PRODUCT;
  payload: string;
}

interface UpdateStockAction {
  type: ActionTypes.UPDATE_STOCK;
  payload: { id: string; change: number };
}

interface OrderAction {
  type: ActionTypes.ADD_ORDER | ActionTypes.UPDATE_ORDER;
  payload: Order;
}

interface DeleteOrderAction {
  type: ActionTypes.DELETE_ORDER;
  payload: string;
}

interface UpdateOrderStatusAction {
  type: ActionTypes.UPDATE_ORDER_STATUS;
  payload: { id: string; status: string };
}

interface CustomerAction {
  type: ActionTypes.ADD_CUSTOMER | ActionTypes.UPDATE_CUSTOMER;
  payload: Customer;
}

interface DeleteCustomerAction {
  type: ActionTypes.DELETE_CUSTOMER;
  payload: string;
}

interface UpdateSettingsAction {
  type: ActionTypes.UPDATE_SETTINGS;
  payload: Partial<Settings>;
}

interface LoadDataAction {
  type: ActionTypes.LOAD_DATA;
  payload: Partial<DataState>;
}

interface SetDataLoadedAction {
  type: ActionTypes.SET_DATA_LOADED;
  payload: boolean;
}

interface ClearDataAction {
  type: ActionTypes.CLEAR_DATA;
}

type DataAction = 
  | ProductAction 
  | DeleteProductAction 
  | UpdateStockAction
  | OrderAction 
  | DeleteOrderAction 
  | UpdateOrderStatusAction
  | CustomerAction 
  | DeleteCustomerAction
  | UpdateSettingsAction
  | LoadDataAction
  | SetDataLoadedAction
  | ClearDataAction;

// Initial state with completely empty arrays - no dummy data
const initialState: DataState = {
  products: [],
  orders: [],
  customers: [],
  settings: {
    storeName: 'My Business',
    ownerName: 'Business Owner',
    email: '<EMAIL>',
    phone: '******-000-0000',
    address: 'Business Address',
    taxRate: 0.08,
    currency: 'BDT',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false,
};

// Reducer function
const dataReducer = (state: DataState, action: DataAction): DataState => {
  switch (action.type) {
    case ActionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [...state.products, { ...action.payload, id: state.nextProductId.toString() }],
        nextProductId: state.nextProductId + 1,
      };

    case ActionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
      };

    case ActionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      };

    case ActionTypes.UPDATE_STOCK:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id
            ? { ...product, stock: Math.max(0, product.stock + action.payload.change) }
            : product
        ),
      };

    case ActionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...state.orders, { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case ActionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case ActionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
      };

    case ActionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, status: action.payload.status as any } : order
        ),
      };

    case ActionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: state.nextCustomerId.toString() }],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case ActionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case ActionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(customer => customer.id !== action.payload),
      };

    case ActionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case ActionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case ActionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case ActionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    default:
      return state;
  }
};

// Context interface
interface DataContextType {
  state: DataState;
  actions: {
    // Products
    addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Product>;
    updateProduct: (product: Product) => Promise<Product>;
    deleteProduct: (id: string) => Promise<void>;
    updateStock: (id: string, change: number) => Promise<void>;

    // Orders
    addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Order>;
    updateOrder: (order: Order) => Promise<Order>;
    deleteOrder: (id: string) => Promise<void>;
    updateOrderStatus: (id: string, status: string) => Promise<void>;

    // Customers
    addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Customer>;
    updateCustomer: (customer: Customer) => Promise<Customer>;
    deleteCustomer: (id: string) => Promise<void>;

    // Settings
    updateSettings: (settings: Partial<Settings>) => void;

    // Data Management
    importData: (importedData: any) => void;
    exportData: () => any;
    clearData: () => Promise<void>;
    forceSave: () => Promise<void>;

    // Enterprise monitoring methods
    getPerformanceScore: () => number;
    getHealthStatus: () => any;
    generateHealthReport: () => any;
    getOverallAppScore: () => any;
    generateSampleData: () => Promise<void>;
  };
}

// Create context
const DataContext = createContext<DataContextType | undefined>(undefined);

// Provider component with performance optimizations
interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  LoggingService.debug('DataProvider rendered', 'DATA_CONTEXT');
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    loadData();
  }, []);

  // Debounced save to prevent excessive writes - reduced to 500ms for snappier feel
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveData();
    }, 500); // Save after 500ms of inactivity for better responsiveness

    return () => clearTimeout(timeoutId);
  }, [state.products, state.orders, state.customers, state.settings]); // Only watch specific state properties

  const loadData = useCallback(async () => {
    const startTime = Date.now();
    try {
      LoggingService.info('Loading data with enterprise optimization...', 'DATA_CONTEXT');
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', 0);

      // Initialize MigrationService (handles SQLite setup and migration)
      await MigrationService.initialize();

      // Data will be loaded from SQLite or start empty

      // Load data using the optimized service
      const [products, orders, customers] = await Promise.all([
        MigrationService.getProducts(),
        MigrationService.getOrders(),
        MigrationService.getCustomers()
      ]);

      // Load settings from AsyncStorage (settings remain in AsyncStorage for simplicity)
      const savedSettings = await AsyncStorage.getItem('bakerySettings');
      const settings = savedSettings ?
        { ...initialState.settings, ...JSON.parse(savedSettings) } :
        initialState.settings;

      LoggingService.info(`Data loaded: ${products.length} products, ${orders.length} orders, ${customers.length} customers`, 'DATA_CONTEXT');
      LoggingService.debug('Performance info', 'DATA_CONTEXT', MigrationService.getPerformanceInfo());

      // Calculate next IDs
      const nextProductId = products.length > 0 ? Math.max(...products.map(p => parseInt(p.id))) + 1 : 1;
      const nextOrderId = orders.length > 0 ? Math.max(...orders.map(o => parseInt(o.id) || 0)) + 1 : 1;
      const nextCustomerId = customers.length > 0 ? Math.max(...customers.map(c => parseInt(c.id))) + 1 : 1;

      const loadedData = {
        products,
        orders,
        customers,
        settings,
        nextProductId,
        nextOrderId,
        nextCustomerId,
      };

      dispatch({ type: ActionTypes.LOAD_DATA, payload: loadedData });

      // Track performance metrics
      const loadTime = Date.now() - startTime;
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', loadTime);
      LoggingService.info('Data loading completed successfully', 'DATA_CONTEXT', {
        loadTime,
        productsCount: products.length,
        ordersCount: orders.length,
        customersCount: customers.length,
      });
    } catch (error) {
      LoggingService.error('Error loading data', 'DATA_CONTEXT', error as Error);
      // Fallback to empty state on error
      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
    }
  }, []);

  const saveData = useCallback(async () => {
    // Only save if data has been loaded to prevent overwriting with empty state
    if (!state.isDataLoaded) {
      return;
    }

    try {
      // Save settings to AsyncStorage
      await AsyncStorage.setItem('bakerySettings', JSON.stringify(state.settings));

      // Note: Products, orders, and customers are automatically saved by MigrationService
      // when using the action creators below. This function now mainly handles settings.

      LoggingService.info('Settings saved successfully', 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error('Error saving settings', 'DATA_CONTEXT', error as Error);
      // Don't crash the app on save errors
    }
  }, [state.settings, state.isDataLoaded]);

  // Memoized action creators for performance with SQLite optimization
  const actions = useMemo(() => ({
    // Products - now with SQLite backend
    addProduct: async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
      const savedProduct = await MigrationService.saveProduct({
        ...product,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: ActionTypes.ADD_PRODUCT, payload: savedProduct });
      return savedProduct;
    },

    updateProduct: async (product: Product): Promise<Product> => {
      const updatedProduct = await MigrationService.saveProduct({
        ...product,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: ActionTypes.UPDATE_PRODUCT, payload: updatedProduct });
      return updatedProduct;
    },

    deleteProduct: async (id: string): Promise<void> => {
      await MigrationService.deleteProduct(id);
      dispatch({ type: ActionTypes.DELETE_PRODUCT, payload: id });
    },

    updateStock: async (id: string, change: number): Promise<void> => {
      // Get current product, update stock, then save
      const products = await MigrationService.getProducts();
      const product = products.find(p => p.id === id);
      if (product) {
        const updatedProduct = {
          ...product,
          stock: Math.max(0, product.stock + change),
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveProduct(updatedProduct);
        dispatch({ type: ActionTypes.UPDATE_STOCK, payload: { id, change } });
      }
    },

    // Orders - now with SQLite backend
    addOrder: async (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> => {
      const orderWithId = {
        ...order,
        id: String(state.nextOrderId).padStart(3, '0'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      const savedOrder = await MigrationService.saveOrder(orderWithId);
      dispatch({ type: ActionTypes.ADD_ORDER, payload: savedOrder });
      return savedOrder;
    },

    updateOrder: async (order: Order): Promise<Order> => {
      const updatedOrder = await MigrationService.saveOrder({
        ...order,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: ActionTypes.UPDATE_ORDER, payload: updatedOrder });
      return updatedOrder;
    },

    deleteOrder: async (id: string): Promise<void> => {
      await MigrationService.deleteOrder(id);
      dispatch({ type: ActionTypes.DELETE_ORDER, payload: id });
    },

    updateOrderStatus: async (id: string, status: string): Promise<void> => {
      const orders = await MigrationService.getOrders();
      const order = orders.find(o => o.id === id);
      if (order) {
        const updatedOrder = {
          ...order,
          status: status as any,
          updatedAt: new Date().toISOString()
        };
        await MigrationService.saveOrder(updatedOrder);
        dispatch({ type: ActionTypes.UPDATE_ORDER_STATUS, payload: { id, status } });
      }
    },

    // Customers - now with SQLite backend
    addCustomer: async (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> => {
      const savedCustomer = await MigrationService.saveCustomer({
        ...customer,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: ActionTypes.ADD_CUSTOMER, payload: savedCustomer });
      return savedCustomer;
    },

    updateCustomer: async (customer: Customer): Promise<Customer> => {
      const updatedCustomer = await MigrationService.saveCustomer({
        ...customer,
        updatedAt: new Date().toISOString()
      });
      dispatch({ type: ActionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
      return updatedCustomer;
    },

    deleteCustomer: async (id: string): Promise<void> => {
      await MigrationService.deleteCustomer(id);
      dispatch({ type: ActionTypes.DELETE_CUSTOMER, payload: id });
    },

    // Settings
    updateSettings: (settings: Partial<Settings>) => dispatch({ type: ActionTypes.UPDATE_SETTINGS, payload: settings }),

    // Data Management
    importData: (importedData: any) => {
      try {
        // Validate imported data
        const validatedData = {
          products: Array.isArray(importedData.products) ? importedData.products : [],
          orders: Array.isArray(importedData.orders) ? importedData.orders : [],
          customers: Array.isArray(importedData.customers) ? importedData.customers : [],
          settings: importedData.settings || state.settings,
        };

        // Update next IDs based on imported data
        const nextProductId = validatedData.products.length > 0 ? Math.max(...validatedData.products.map((p: Product) => parseInt(p.id))) + 1 : 1;
        const nextOrderId = validatedData.orders.length > 0 ? Math.max(...validatedData.orders.map((o: Order) => parseInt(o.id))) + 1 : 1;
        const nextCustomerId = validatedData.customers.length > 0 ? Math.max(...validatedData.customers.map((c: Customer) => parseInt(c.id))) + 1 : 1;

        dispatch({
          type: ActionTypes.LOAD_DATA,
          payload: {
            ...validatedData,
            nextProductId,
            nextOrderId,
            nextCustomerId,
          }
        });

        LoggingService.info(`Data imported successfully: ${validatedData.products.length} products, ${validatedData.orders.length} orders, ${validatedData.customers.length} customers`, 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Error importing data', 'DATA_CONTEXT', error as Error);
      }
    },

    exportData: () => {
      return {
        products: state.products,
        orders: state.orders,
        customers: state.customers,
        settings: state.settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
    },

    clearData: async () => {
      try {
        await AsyncStorage.removeItem('bakeryData');
        dispatch({ type: ActionTypes.CLEAR_DATA });
        LoggingService.info('All data cleared successfully', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Error clearing data', 'DATA_CONTEXT', error as Error);
      }
    },

    // Force save data immediately (for critical operations)
    forceSave: async () => {
      await saveData();
    },

    // Enterprise monitoring methods
    getPerformanceScore: () => {
      return PerformanceOptimizer.getScore();
    },

    getHealthStatus: () => {
      return HealthMonitor.getHealthStatus();
    },

    generateHealthReport: () => {
      return HealthMonitor.generateHealthReport();
    },

    getOverallAppScore: () => {
      const performanceScore = PerformanceOptimizer.getScore();
      const healthScore = HealthMonitor.getOverallScore();

      const overallScore = (performanceScore + healthScore) / 2;

      LoggingService.info('Overall app score calculated', 'ENTERPRISE', {
        performanceScore,
        healthScore,
        overallScore,
      });

      return {
        overallScore: Math.round(overallScore * 100) / 100,
        breakdown: {
          performance: performanceScore,
          health: healthScore,
        },
        grade: overallScore >= 95 ? 'A+' : overallScore >= 90 ? 'A' : overallScore >= 85 ? 'B+' : 'B',
        status: overallScore >= 95 ? 'Excellent' : overallScore >= 90 ? 'Very Good' : overallScore >= 85 ? 'Good' : 'Fair',
      };
    },

    // Generate sample data (100 products and 100 customers)
    generateSampleData: async () => {
      try {
        await MigrationService.forceRegenerateSampleData();
        // Reload data after generation
        await loadData();
        LoggingService.info('Sample data generated and reloaded successfully', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Failed to generate sample data', 'DATA_CONTEXT', error as Error);
      }
    },

  }), [dispatch, state.products, state.orders, state.customers, state.settings, state.nextOrderId, saveData, loadData]);

  return (
    <DataContext.Provider value={{ state, actions }}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;