import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { lightTheme, darkTheme } from '../theme/theme';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS, COMPONENT_SIZES, LAYOUT, ANIMATIONS, OPACITY, Z_INDEX, BREAKPOINTS, COMMON_STYLES } from '../theme/designTokens';
import LoggingService from '../services/LoggingService';

// Theme interfaces
interface ThemeColors {
  primary: string;
  primaryContainer: string;
  secondary: string;
  secondaryContainer: string;
  tertiary: string;
  tertiaryContainer: string;
  surface: string;
  surfaceVariant: string;
  background: string;
  error: string;
  errorContainer: string;
  onPrimary: string;
  onPrimaryContainer: string;
  onSecondary: string;
  onSecondaryContainer: string;
  onTertiary: string;
  onTertiaryContainer: string;
  onSurface: string;
  onSurfaceVariant: string;
  onError: string;
  onErrorContainer: string;
  onBackground: string;
  outline: string;
  outlineVariant: string;
  inverseSurface: string;
  inverseOnSurface: string;
  inversePrimary: string;
}

interface Theme {
  colors: ThemeColors;
  spacing: typeof SPACING;
  borderRadius: typeof BORDER_RADIUS;
  typography: typeof TYPOGRAPHY;
  shadows: typeof SHADOWS;
  componentSizes: typeof COMPONENT_SIZES;
  layout: typeof LAYOUT;
  animations: typeof ANIMATIONS;
  opacity: typeof OPACITY;
  zIndex: typeof Z_INDEX;
  breakpoints: typeof BREAKPOINTS;
  commonStyles: typeof COMMON_STYLES;
  mode: 'light' | 'dark';
}

interface ThemeContextType {
  isDarkMode: boolean;
  theme: Theme;
  toggleTheme: () => Promise<void>;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load theme preference on app start
  useEffect(() => {
    loadThemePreference();
  }, []);

  const loadThemePreference = async (): Promise<void> => {
    try {
      const savedTheme = await AsyncStorage.getItem('darkMode');
      if (savedTheme !== null) {
        setIsDarkMode(JSON.parse(savedTheme));
      }
    } catch (error) {
      LoggingService.error('Error loading theme preference', 'THEME', error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTheme = async (): Promise<void> => {
    try {
      const newTheme = !isDarkMode;
      setIsDarkMode(newTheme);
      await AsyncStorage.setItem('darkMode', JSON.stringify(newTheme));
      LoggingService.info(`Theme switched to ${newTheme ? 'dark' : 'light'} mode`, 'THEME');
    } catch (error) {
      LoggingService.error('Error saving theme preference', 'THEME', error as Error);
    }
  };

  const theme: Theme = {
    ...(isDarkMode ? darkTheme : lightTheme),
    spacing: SPACING,
    borderRadius: BORDER_RADIUS,
    typography: TYPOGRAPHY,
    shadows: SHADOWS,
    componentSizes: COMPONENT_SIZES,
    layout: LAYOUT,
    animations: ANIMATIONS,
    opacity: OPACITY,
    zIndex: Z_INDEX,
    breakpoints: BREAKPOINTS,
    commonStyles: COMMON_STYLES,
    mode: isDarkMode ? 'dark' : 'light',
  };

  const value: ThemeContextType = {
    isDarkMode,
    theme,
    toggleTheme,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  if (!context.theme || !context.theme.colors) {
    LoggingService.warn('Theme object is missing colors property', 'THEME', context.theme);
  }
  return context;
};

export default ThemeContext;