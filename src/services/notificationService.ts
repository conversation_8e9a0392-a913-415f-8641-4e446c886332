import React, { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { StorageService } from './storageService';
import LoggingService from './LoggingService';
import { FEATURE_FLAGS } from '../config/constants';

/**
 * Notification types
 */
export const NotificationTypes = {
  LOW_STOCK: 'LOW_STOCK',
  ORDER_UPDATE: 'ORDER_UPDATE',
  DAILY_SUMMARY: 'DAILY_SUMMARY',
  SYSTEM: 'SYSTEM',
  REMINDER: 'REMINDER',
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS',
} as const;

/**
 * Notification priorities
 */
export const NotificationPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
} as const;

export type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];
export type NotificationPriorityType = typeof NotificationPriority[keyof typeof NotificationPriority];

export interface NotificationData {
  items?: any[];
  orders?: number;
  sales?: number;
  action?: () => void;
  actionText?: string;
  [key: string]: any;
}

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriorityType;
  data: NotificationData;
  timestamp: string;
  read: boolean;
  persistent: boolean;
}

export interface NotificationFilter {
  type?: NotificationType;
  unreadOnly?: boolean;
  priority?: NotificationPriorityType;
}

export interface CreateNotificationParams {
  id?: string;
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriorityType;
  data?: NotificationData;
  timestamp?: string;
  read?: boolean;
  persistent?: boolean;
}

type NotificationEventType = 
  | 'notification_created'
  | 'notification_read'
  | 'all_notifications_read'
  | 'notification_deleted'
  | 'all_notifications_cleared'
  | 'notification_seeded';

type NotificationListener = (event: NotificationEventType, data?: Notification | null) => void;

/**
 * Notification service for managing app notifications
 */
export class NotificationService {
  private static notifications: Notification[] = [];
  private static listeners: NotificationListener[] = [];
  private static isEnabled: boolean = FEATURE_FLAGS.ENABLE_PUSH_NOTIFICATIONS;
  private static periodicCheckInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize notification service
   */
  static async initialize(): Promise<void> {
    try {
      await this.loadNotifications();
      this.schedulePeriodicChecks();
      LoggingService.info('Notification service initialized', 'NOTIFICATION');
    } catch (error) {
      LoggingService.error('Failed to initialize notification service', 'NOTIFICATION', error as Error);
    }
  }

  /**
   * Create a new notification
   */
  static async createNotification(params: CreateNotificationParams): Promise<Notification> {
    const {
      id = Date.now().toString(),
      type,
      title,
      message,
      priority = NotificationPriority.MEDIUM,
      data = {},
      timestamp = new Date().toISOString(),
      read = false,
      persistent = false,
    } = params;

    const notification: Notification = {
      id,
      type,
      title,
      message,
      priority,
      data,
      timestamp,
      read,
      persistent,
    };

    this.notifications.unshift(notification);
    await this.saveNotifications();
    this.notifyListeners('notification_created', notification);

    // Show immediate alert for high priority notifications
    if (priority === NotificationPriority.HIGH || priority === NotificationPriority.URGENT) {
      this.showAlert(notification);
    }

    LoggingService.info(`Notification created: ${title}`, 'NOTIFICATION');
    return notification;
  }

  /**
   * Get all notifications
   */
  static getNotifications(filter: NotificationFilter = {}): Notification[] {
    let filtered = [...this.notifications];

    if (filter.type) {
      filtered = filtered.filter(n => n.type === filter.type);
    }

    if (filter.unreadOnly) {
      filtered = filtered.filter(n => !n.read);
    }

    if (filter.priority) {
      filtered = filtered.filter(n => n.priority === filter.priority);
    }

    return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      await this.saveNotifications();
      this.notifyListeners('notification_read', notification);
    }
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead(): Promise<void> {
    this.notifications.forEach(n => n.read = true);
    await this.saveNotifications();
    this.notifyListeners('all_notifications_read');
  }

  /**
   * Delete notification
   */
  static async deleteNotification(notificationId: string): Promise<void> {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      const notification = this.notifications[index];
      this.notifications.splice(index, 1);
      await this.saveNotifications();
      this.notifyListeners('notification_deleted', notification);
    }
  }

  /**
   * Clear all notifications
   */
  static async clearAll(): Promise<void> {
    this.notifications = [];
    await this.saveNotifications();
    this.notifyListeners('all_notifications_cleared');
  }

  /**
   * Get unread count
   */
  static getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Add event listener
   */
  static addListener(callback: NotificationListener): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index !== -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  private static notifyListeners(event: NotificationEventType, data: Notification | null = null): void {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        LoggingService.error('Error in notification listener', 'NOTIFICATION', error as Error);
      }
    });
  }

  /**
   * Show alert for notification
   */
  private static showAlert(notification: Notification): void {
    if (!this.isEnabled) return;

    const buttons: any[] = [
      {
        text: 'Dismiss',
        style: 'cancel',
        onPress: () => this.markAsRead(notification.id),
      },
    ];

    if (notification.data.action) {
      buttons.push({
        text: notification.data.actionText || 'View',
        onPress: () => {
          this.markAsRead(notification.id);
          notification.data.action?.();
        },
      });
    }

    Alert.alert(notification.title, notification.message, buttons);
  }

  /**
   * Save notifications to storage
   */
  private static async saveNotifications(): Promise<void> {
    try {
      // Keep only last 100 notifications and all persistent ones
      const toKeep = this.notifications
        .filter(n => n.persistent)
        .concat(
          this.notifications
            .filter(n => !n.persistent)
            .slice(0, 100)
        );

      this.notifications = toKeep;
      await StorageService.set('notifications', this.notifications);
    } catch (error) {
      LoggingService.error('Failed to save notifications', 'NOTIFICATION', error as Error);
    }
  }

  /**
   * Load notifications from storage
   */
  private static async loadNotifications(): Promise<void> {
    try {
      const saved = await StorageService.get('notifications') || [];
      this.notifications = saved as Notification[];
    } catch (error) {
      LoggingService.error('Failed to load notifications', 'NOTIFICATION', error as Error);
      this.notifications = [];
    }
  }

  /**
   * Schedule periodic checks for business logic
   */
  private static schedulePeriodicChecks(): void {
    // Only schedule if not already scheduled
    if (this.periodicCheckInterval) {
      return;
    }

    // Check every 10 minutes (reduced frequency)
    this.periodicCheckInterval = setInterval(() => {
      this.checkLowStock();
      this.checkDailySummary();
    }, 10 * 60 * 1000);
  }

  /**
   * Clear periodic checks
   */
  static clearPeriodicChecks(): void {
    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }
  }

  /**
   * Check for low stock items
   */
  private static async checkLowStock(): Promise<void> {
    try {
      const bakeryData = await StorageService.get('bakeryData');
      if (!bakeryData?.products) return;

      const lowStockItems = bakeryData.products.filter((product: any) => product.stock <= 5);

      if (lowStockItems.length > 0) {
        const lastCheck = await StorageService.get('lastLowStockCheck');
        const now = new Date().toDateString();

        // Only notify once per day
        if (lastCheck !== now) {
          await this.createNotification({
            type: NotificationTypes.LOW_STOCK,
            title: 'Low Stock Alert',
            message: `${lowStockItems.length} item(s) are running low on stock`,
            priority: NotificationPriority.HIGH,
            data: {
              items: lowStockItems,
              action: () => {
                // Navigate to products screen
              },
              actionText: 'View Products',
            },
          });

          await StorageService.set('lastLowStockCheck', now);
        }
      }
    } catch (error) {
      LoggingService.error('Failed to check low stock', 'NOTIFICATION', error as Error);
    }
  }

  /**
   * Check for daily summary
   */
  private static async checkDailySummary(): Promise<void> {
    try {
      const lastSummary = await StorageService.get('lastDailySummary');
      const today = new Date().toDateString();

      // Send daily summary at 6 PM
      const now = new Date();
      const isAfter6PM = now.getHours() >= 18;

      if (lastSummary !== today && isAfter6PM) {
        const bakeryData = await StorageService.get('bakeryData');
        if (!bakeryData?.orders) return;

        const todayOrders = bakeryData.orders.filter((order: any) =>
          order.date === new Date().toLocaleDateString()
        );

        const totalSales = todayOrders.reduce((sum: number, order: any) => sum + order.total, 0);

        await this.createNotification({
          type: NotificationTypes.DAILY_SUMMARY,
          title: 'Daily Summary',
          message: `Today: ${todayOrders.length} orders, ${totalSales.toFixed(2)} in sales`,
          priority: NotificationPriority.MEDIUM,
          data: {
            orders: todayOrders.length,
            sales: totalSales,
            action: () => {
              // Navigate to reports
            },
            actionText: 'View Report',
          },
        });

        await StorageService.set('lastDailySummary', today);
      }
    } catch (error) {
      LoggingService.error('Failed to check daily summary', 'NOTIFICATION', error as Error);
    }
  }

  /**
   * Seed dummy notifications for development/demo
   */
  static async seedDummyNotifications(): Promise<void> {
    const dummy: Notification[] = [
      {
        id: '1',
        type: NotificationTypes.SYSTEM,
        title: 'Welcome to TailorZap!',
        message: 'Your app is ready to use. Explore features and manage your business easily.',
        priority: NotificationPriority.MEDIUM,
        data: {},
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        read: false,
        persistent: false,
      },
      {
        id: '2',
        type: NotificationTypes.ORDER_UPDATE,
        title: 'New Order Received',
        message: 'Order #1234 has been placed by John Doe.',
        priority: NotificationPriority.HIGH,
        data: {
          actionText: 'View Order',
          action: () => {},
        },
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        read: false,
        persistent: false,
      },
      {
        id: '3',
        type: NotificationTypes.REMINDER,
        title: 'Inventory Low',
        message: 'Stock for Croissants is below 10. Consider restocking soon.',
        priority: NotificationPriority.MEDIUM,
        data: {},
        timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
        read: true,
        persistent: false,
      },
    ];
    this.notifications = dummy;
    await this.saveNotifications();
    this.notifyListeners('notification_seeded');
  }
}

export interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: string) => void;
  clearAll: () => void;
}

/**
 * React hook for notifications
 */
export const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  useEffect(() => {
    const updateNotifications = () => {
      setNotifications(NotificationService.getNotifications());
      setUnreadCount(NotificationService.getUnreadCount());
    };

    updateNotifications();

    const unsubscribe = NotificationService.addListener(() => {
      updateNotifications();
    });

    return unsubscribe;
  }, []);

  const markAsRead = useCallback((id: string) => {
    NotificationService.markAsRead(id);
  }, []);

  const markAllAsRead = useCallback(() => {
    NotificationService.markAllAsRead();
  }, []);

  const deleteNotification = useCallback((id: string) => {
    NotificationService.deleteNotification(id);
  }, []);

  const clearAll = useCallback(() => {
    NotificationService.clearAll();
  }, []);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
  };
};