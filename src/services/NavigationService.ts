/**
 * NavigationService - Centralized navigation management
 * Provides unified navigation methods and route management
 */

import { createNavigationContainerRef, NavigationContainerRef, ParamListBase } from '@react-navigation/native';
import LoggingService from './LoggingService';

export const navigationRef = createNavigationContainerRef<ParamListBase>();

interface RouteHistoryItem {
  name: string;
  params?: any;
  timestamp: number;
}

interface NavigationAnalytics {
  totalNavigations: number;
  routeCounts: Record<string, number>;
  mostVisitedRoute: string;
  currentRoute: string | null;
  history: RouteHistoryItem[];
}

type NavigationListener = (state: any) => void;

type QuickAction = 'add-product' | 'add-order' | 'scan' | 'add-customer';

class NavigationService {
  private currentRoute: any = null;
  private routeHistory: RouteHistoryItem[] = [];
  private navigationListeners: NavigationListener[] = [];

  // Core navigation methods
  navigate(name: string, params?: any): void {
    LoggingService.debug(`Attempting to navigate to ${name}`, 'NAVIGATION', params);
    if (navigationRef.isReady()) {
      LoggingService.debug(`Navigation ref is ready, navigating to ${name}`, 'NAVIGATION');
      (navigationRef as any).navigate(name, params);
      this.addToHistory(name, params);
      LoggingService.info(`Successfully navigated to ${name}`, 'NAVIGATION');
    } else {
      LoggingService.error(`Navigation ref is not ready for ${name}`, 'NAVIGATION');
    }
  }

  goBack(): void {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.goBack();
    }
  }

  reset(state: any): void {
    if (navigationRef.isReady()) {
      navigationRef.reset(state);
      this.routeHistory = [];
    }
  }

  // Tab navigation methods
  navigateToTab(tabName: string): void {
    this.navigate('Main', { screen: tabName });
  }

  // Modal navigation methods
  openModal(modalName: string, params?: any): void {
    this.navigate(modalName, params);
  }

  closeModal(): void {
    this.goBack();
  }

  // Settings navigation methods
  openSettings(settingsScreen: string = 'MyProfile'): void {
    if (settingsScreen === 'Profile' || settingsScreen === 'MyProfile') {
      this.navigate('MyProfile');
    } else {
      this.navigateToTab('Settings');
    }
  }

  // Quick actions navigation
  openQuickAction(action: QuickAction, params?: any): void {
    switch (action) {
      case 'add-product':
        this.navigate('AddProduct', params);
        break;
      case 'add-order':
        this.navigate('AddOrder', params);
        break;
      case 'scan':
        this.navigateToTab('Scan');
        break;
      case 'add-customer':
        this.navigate('Customers', { openAddCustomer: true });
        break;
      default:
        LoggingService.warn(`Unknown quick action: ${action}`, 'NAVIGATION');
    }
  }

  // Route management
  getCurrentRoute(): any {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute();
    }
    return null;
  }

  getCurrentRouteName(): string | null {
    const route = this.getCurrentRoute();
    return route?.name || null;
  }

  private addToHistory(name: string, params?: any): void {
    this.routeHistory.push({ name, params, timestamp: Date.now() });
    // Keep only last 10 routes
    if (this.routeHistory.length > 10) {
      this.routeHistory.shift();
    }
  }

  getRouteHistory(): RouteHistoryItem[] {
    return this.routeHistory;
  }

  // Navigation state listeners
  addNavigationListener(listener: NavigationListener): void {
    this.navigationListeners.push(listener);
  }

  removeNavigationListener(listener: NavigationListener): void {
    this.navigationListeners = this.navigationListeners.filter(l => l !== listener);
  }

  notifyNavigationChange(state: any): void {
    this.navigationListeners.forEach(listener => listener(state));
  }

  // Utility methods
  isTabRoute(routeName: string): boolean {
    const tabRoutes = ['Dashboard', 'Scan', 'Orders', 'Settings'];
    return tabRoutes.includes(routeName);
  }

  isModalRoute(routeName: string): boolean {
    const modalRoutes = ['Products', 'AddProduct', 'AddOrder'];
    return modalRoutes.includes(routeName);
  }

  getTabFromRoute(routeName: string): string {
    if (this.isTabRoute(routeName)) {
      return routeName;
    }
    // Handle nested routes
    if (routeName === 'SettingsMain') {
      return 'Settings';
    }
    if (routeName === 'MyProfile') {
      return 'Dashboard'; // MyProfile can be accessed from any tab
    }
    if (routeName === 'AddProduct') {
      return 'Dashboard'; // AddProduct can be accessed from any tab
    }
    if (routeName === 'AddOrder') {
      return 'Orders'; // AddOrder is related to orders
    }
    if (routeName === 'ImportData') {
      return 'Settings'; // ImportData is accessed from Settings
    }
    if (routeName === 'ActivityLog') {
      return 'Settings'; // ActivityLog is accessed from Settings
    }
    return 'Dashboard'; // Default
  }

  // Deep linking support
  buildDeepLink(routeName: string, params?: Record<string, any>): string {
    let link = `sweetdelights://`;

    if (this.isTabRoute(routeName)) {
      link += `tab/${routeName.toLowerCase()}`;
    } else if (this.isModalRoute(routeName)) {
      link += `modal/${routeName.toLowerCase()}`;
    } else {
      link += `screen/${routeName.toLowerCase()}`;
    }

    if (params) {
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');
      link += `?${queryString}`;
    }

    return link;
  }

  // Navigation analytics
  getNavigationAnalytics(): NavigationAnalytics {
    const routeCounts: Record<string, number> = {};
    this.routeHistory.forEach(route => {
      routeCounts[route.name] = (routeCounts[route.name] || 0) + 1;
    });

    const mostVisitedRoute = Object.keys(routeCounts).reduce((a, b) =>
      routeCounts[a] > routeCounts[b] ? a : b, 'Dashboard'
    );

    return {
      totalNavigations: this.routeHistory.length,
      routeCounts,
      mostVisitedRoute,
      currentRoute: this.getCurrentRouteName(),
      history: this.routeHistory.slice(-5) // Last 5 routes
    };
  }
}

// Create singleton instance
const navigationService = new NavigationService();

export default navigationService;

// Export convenience methods
export const navigate = (name: string, params?: any): void => navigationService.navigate(name, params);
export const goBack = (): void => navigationService.goBack();
export const navigateToTab = (tabName: string): void => navigationService.navigateToTab(tabName);
export const openModal = (modalName: string, params?: any): void => navigationService.openModal(modalName, params);
export const openSettings = (settingsScreen?: string): void => navigationService.openSettings(settingsScreen);
export const openQuickAction = (action: QuickAction, params?: any): void => navigationService.openQuickAction(action, params);