/**
 * Migration Service - Seamless transition from AsyncStorage to SQLite
 * Handles data migration and fallback strategies
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';
import LoggingService from './LoggingService';
import { Product, Order, Customer } from '../types';

interface MigrationData {
  products: Product[];
  orders: Order[];
  customers: Customer[];
}

interface PerformanceInfo {
  migrationCompleted: boolean;
  usingSQLite: boolean;
  dataSource: 'SQLite' | 'AsyncStorage';
  migrationTime?: number;
}

class MigrationService {
  private migrationCompleted: boolean = false;
  private useSQLite: boolean = false;

  async initialize(): Promise<void> {
    try {
      // Check if migration has been completed before
      const migrationStatus = await AsyncStorage.getItem('sqlite_migration_completed');

      if (migrationStatus === 'true') {
        // Migration already completed, use SQLite
        await SQLiteService.initialize();
        this.useSQLite = true;
        this.migrationCompleted = true;
        LoggingService.info('Using SQLite database (migration previously completed)', 'MIGRATION');

        // Generate sample data if needed
        await this.generateSampleDataIfNeeded();
        return;
      }

      // Try to initialize SQLite
      await SQLiteService.initialize();

      // Migrate existing data from AsyncStorage
      await this.migrateData();

      // Mark migration as completed
      await AsyncStorage.setItem('sqlite_migration_completed', 'true');

      this.useSQLite = true;
      this.migrationCompleted = true;
      LoggingService.info('SQLite migration completed successfully', 'MIGRATION');

      // Generate sample data if needed
      await this.generateSampleDataIfNeeded();

    } catch (error) {
      LoggingService.warn('SQLite initialization failed, falling back to AsyncStorage', 'MIGRATION', error);
      // Fallback to AsyncStorage if SQLite fails
      this.useSQLite = false;
      this.migrationCompleted = false;
    }
  }

  private async migrateData(): Promise<void> {
    LoggingService.info('Starting data migration from AsyncStorage to SQLite', 'MIGRATION');

    try {
      // Get all existing data from AsyncStorage
      const [products, orders, customers, expenses, reconciliations] = await Promise.all([
        this.getAsyncStorageData('bakeryData'),
        this.getAsyncStorageData('orders'),
        this.getAsyncStorageData('customers'),
        this.getAsyncStorageData('financial_expenses'),
        this.getAsyncStorageData('cash_reconciliations')
      ]);

      // Prepare migration data
      const migrationData: MigrationData = {
        products: products?.products || [],
        orders: orders || [],
        customers: customers || []
      };

      // Check if there's any data to migrate
      const hasData = migrationData.products.length > 0 ||
                     migrationData.orders.length > 0 ||
                     migrationData.customers.length > 0;

      if (hasData) {
        await SQLiteService.migrateFromAsyncStorage(migrationData);
        LoggingService.info('Data migration completed', 'MIGRATION', {
          products: migrationData.products.length,
          orders: migrationData.orders.length,
          customers: migrationData.customers.length
        });
      } else {
        LoggingService.info('No existing data found to migrate', 'MIGRATION');
      }

    } catch (error) {
      LoggingService.error('Migration failed', 'MIGRATION', error as Error);
      throw error;
    }
  }

  private async getAsyncStorageData(key: string): Promise<any> {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      LoggingService.warn(`Failed to get AsyncStorage data for key ${key}`, 'MIGRATION', error);
      return null;
    }
  }

  // Data access methods that work with both SQLite and AsyncStorage
  async getProducts(): Promise<Product[]> {
    if (this.useSQLite) {
      return await SQLiteService.getProducts();
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData');
      return data?.products || [];
    }
  }

  async getOrders(): Promise<Order[]> {
    if (this.useSQLite) {
      return await SQLiteService.getOrders();
    } else {
      // Fallback to AsyncStorage
      return await this.getAsyncStorageData('orders') || [];
    }
  }

  async getCustomers(): Promise<Customer[]> {
    if (this.useSQLite) {
      return await SQLiteService.getCustomers();
    } else {
      // Fallback to AsyncStorage
      return await this.getAsyncStorageData('customers') || [];
    }
  }

  async saveProduct(product: Partial<Product> & { name: string; price: number }): Promise<Product> {
    if (this.useSQLite) {
      return await SQLiteService.saveProduct(product);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData') || { products: [] };
      const now = new Date().toISOString();
      
      if (product.id) {
        // Update existing
        const index = data.products.findIndex((p: Product) => p.id === product.id);
        if (index !== -1) {
          data.products[index] = { ...product, updatedAt: now };
        }
      } else {
        // Add new
        const newProduct = {
          ...product,
          id: Date.now().toString(),
          createdAt: now,
          updatedAt: now
        };
        data.products.push(newProduct);
        product = newProduct as Product;
      }
      
      await AsyncStorage.setItem('bakeryData', JSON.stringify(data));
      return product as Product;
    }
  }

  async saveOrder(order: Partial<Order> & { id: string; customerName: string; total: number }): Promise<Order> {
    if (this.useSQLite) {
      return await SQLiteService.saveOrder(order);
    } else {
      // Fallback to AsyncStorage
      const orders = await this.getAsyncStorageData('orders') || [];
      const now = new Date().toISOString();
      
      const existingIndex = orders.findIndex((o: Order) => o.id === order.id);
      const updatedOrder = { ...order, updatedAt: now };
      
      if (existingIndex !== -1) {
        orders[existingIndex] = updatedOrder;
      } else {
        orders.push({ ...updatedOrder, createdAt: now });
      }
      
      await AsyncStorage.setItem('orders', JSON.stringify(orders));
      return updatedOrder as Order;
    }
  }

  async saveCustomer(customer: Partial<Customer> & { name: string }): Promise<Customer> {
    if (this.useSQLite) {
      return await SQLiteService.saveCustomer(customer);
    } else {
      // Fallback to AsyncStorage
      const customers = await this.getAsyncStorageData('customers') || [];
      const now = new Date().toISOString();
      
      if (customer.id) {
        // Update existing
        const index = customers.findIndex((c: Customer) => c.id === customer.id);
        if (index !== -1) {
          customers[index] = { ...customer, updatedAt: now };
        }
      } else {
        // Add new
        const newCustomer = {
          ...customer,
          id: Date.now().toString(),
          createdAt: now,
          updatedAt: now
        };
        customers.push(newCustomer);
        customer = newCustomer as Customer;
      }
      
      await AsyncStorage.setItem('customers', JSON.stringify(customers));
      return customer as Customer;
    }
  }

  async deleteProduct(id: string): Promise<void> {
    if (this.useSQLite) {
      await SQLiteService.deleteProduct(parseInt(id));
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData') || { products: [] };
      data.products = data.products.filter((p: Product) => p.id !== id);
      await AsyncStorage.setItem('bakeryData', JSON.stringify(data));
    }
  }

  async deleteOrder(id: string): Promise<void> {
    if (this.useSQLite) {
      await SQLiteService.deleteOrder(id);
    } else {
      // Fallback to AsyncStorage
      const orders = await this.getAsyncStorageData('orders') || [];
      const filteredOrders = orders.filter((o: Order) => o.id !== id);
      await AsyncStorage.setItem('orders', JSON.stringify(filteredOrders));
    }
  }

  async deleteCustomer(id: string): Promise<void> {
    if (this.useSQLite) {
      await SQLiteService.deleteCustomer(parseInt(id));
    } else {
      // Fallback to AsyncStorage
      const customers = await this.getAsyncStorageData('customers') || [];
      const filteredCustomers = customers.filter((c: Customer) => c.id !== id);
      await AsyncStorage.setItem('customers', JSON.stringify(filteredCustomers));
    }
  }

  // Sample data generation
  private async generateSampleDataIfNeeded(): Promise<void> {
    try {
      const [products, customers] = await Promise.all([
        this.getProducts(),
        this.getCustomers()
      ]);

      // Only generate if we have less than 10 products and customers
      if (products.length < 10 && customers.length < 10) {
        LoggingService.info('Generating sample data', 'MIGRATION');
        await this.generateSampleData();
        LoggingService.info('Sample data generated successfully', 'MIGRATION');
      }
    } catch (error) {
      LoggingService.warn('Failed to generate sample data', 'MIGRATION', error);
    }
  }

  private async generateSampleData(): Promise<void> {
    const startTime = Date.now();

    // Generate 100 sample products
    const products = this.generateSampleProducts(100);

    // Generate 100 sample customers
    const customers = this.generateSampleCustomers(100);

    // Save products in batches for better performance
    LoggingService.info('Saving 100 products', 'MIGRATION');
    for (const product of products) {
      await this.saveProduct(product as any);
    }

    // Save customers in batches for better performance
    LoggingService.info('Saving 100 customers', 'MIGRATION');
    for (const customer of customers) {
      await this.saveCustomer(customer as any);
    }

    const endTime = Date.now();
    LoggingService.info(`Generated 100 products and 100 customers in ${endTime - startTime}ms`, 'MIGRATION');
  }

  private generateSampleProducts(count: number): Partial<Product>[] {
    const categories = ['Cakes', 'Pastries', 'Bread', 'Cookies', 'Beverages', 'Desserts'];
    const products: Partial<Product>[] = [];

    for (let i = 1; i <= count; i++) {
      products.push({
        name: `Sample Product ${i}`,
        description: `This is a sample product description for product ${i}`,
        price: Math.floor(Math.random() * 50) + 10,
        stock: Math.floor(Math.random() * 100) + 10,
        sku: `SKU${i.toString().padStart(3, '0')}`,
        category: categories[Math.floor(Math.random() * categories.length)],
        isActive: true
      });
    }

    return products;
  }

  private generateSampleCustomers(count: number): Partial<Customer>[] {
    const customers: Partial<Customer>[] = [];

    for (let i = 1; i <= count; i++) {
      customers.push({
        name: `Sample Customer ${i}`,
        email: `customer${i}@example.com`,
        phone: `+1555${i.toString().padStart(7, '0')}`,
        address: `${i} Sample Street, Sample City, SC ${i.toString().padStart(5, '0')}`,
        totalOrders: Math.floor(Math.random() * 20),
        totalSpent: Math.floor(Math.random() * 1000) + 100,
        isActive: true
      });
    }

    return customers;
  }

  // Performance and status methods
  getPerformanceInfo(): PerformanceInfo {
    return {
      migrationCompleted: this.migrationCompleted,
      usingSQLite: this.useSQLite,
      dataSource: this.useSQLite ? 'SQLite' : 'AsyncStorage'
    };
  }

  isUsingSQLite(): boolean {
    return this.useSQLite;
  }

  isMigrationCompleted(): boolean {
    return this.migrationCompleted;
  }

  async forceRegenerateSampleData(): Promise<void> {
    if (!this.useSQLite) {
      LoggingService.info('SQLite not available, cannot generate sample data', 'MIGRATION');
      return;
    }

    try {
      LoggingService.info('Force regenerating sample data', 'MIGRATION');

      // Clear existing data
      await SQLiteService.clearAllData();

      // Generate new sample data
      await this.generateSampleData();

      LoggingService.info('Sample data regenerated successfully', 'MIGRATION');
    } catch (error) {
      LoggingService.error('Failed to regenerate sample data', 'MIGRATION', error as Error);
    }
  }
}

export default new MigrationService();