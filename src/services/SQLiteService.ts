/**
 * SQLite Database Service for Optimal Performance
 * Handles all database operations with proper indexing and optimization
 */

import * as SQLite from 'expo-sqlite';
import LoggingService from './LoggingService';
import { Product, Order, Customer, OrderItem } from '../types';

interface DatabaseFilters {
  isActive?: boolean;
  category?: string;
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

interface ParsedProduct extends Omit<Product, 'isActive' | 'isFeatured' | 'tags'> {
  isActive: number;
  isFeatured: number;
  tags: string;
}

interface ParsedOrder extends Omit<Order, 'items'> {
  items: string | null;
}

interface ParsedCustomer extends Omit<Customer, 'isVIP' | 'tags'> {
  isVIP: number;
  tags: string;
}

class SQLiteService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.db = await SQLite.openDatabaseAsync('bakery_app.db');
      await this.createTables();
      await this.createIndexes();
      this.isInitialized = true;
      LoggingService.info('SQLite database initialized successfully', 'DATABASE');
    } catch (error) {
      LoggingService.error('Failed to initialize SQLite database', 'DATABASE', error as Error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const tables = [
      // Products table
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER DEFAULT 0,
        sku TEXT UNIQUE,
        barcode TEXT,
        category TEXT,
        image TEXT,
        isActive INTEGER DEFAULT 1,
        isFeatured INTEGER DEFAULT 0,
        tags TEXT, -- JSON string
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Orders table
      `CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customer TEXT,
        email TEXT,
        phone TEXT,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        status TEXT NOT NULL,
        orderType TEXT DEFAULT 'pickup',
        subtotal REAL NOT NULL,
        tax REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        image TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Order items table
      `CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId TEXT NOT NULL,
        productId INTEGER NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        price REAL NOT NULL,
        total REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES orders (id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products (id)
      )`,

      // Customers table
      `CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        notes TEXT,
        isVIP INTEGER DEFAULT 0,
        totalOrders INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        tags TEXT, -- JSON string
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Financial expenses table
      `CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        date TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Cash reconciliations table
      `CREATE TABLE IF NOT EXISTS cash_reconciliations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        expectedCash REAL NOT NULL,
        actualCash REAL NOT NULL,
        difference REAL NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`
    ];

    for (const table of tables) {
      await this.db.execAsync(table);
    }
  }

  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const indexes = [
      // Product indexes
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)',
      'CREATE INDEX IF NOT EXISTS idx_products_category ON products (category)',
      'CREATE INDEX IF NOT EXISTS idx_products_sku ON products (sku)',
      'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)',
      'CREATE INDEX IF NOT EXISTS idx_products_active ON products (isActive)',
      'CREATE INDEX IF NOT EXISTS idx_products_featured ON products (isFeatured)',

      // Order indexes
      'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders (date)',
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders (customerName)',
      'CREATE INDEX IF NOT EXISTS idx_orders_total ON orders (total)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created ON orders (createdAt)',

      // Order items indexes
      'CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items (orderId)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items (productId)',

      // Customer indexes
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers (name)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers (email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_vip ON customers (isVIP)',

      // Expense indexes
      'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses (category)',
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date)',

      // Cash reconciliation indexes
      'CREATE INDEX IF NOT EXISTS idx_cash_date ON cash_reconciliations (date)'
    ];

    for (const index of indexes) {
      await this.db.execAsync(index);
    }
  }

  // Product operations
  async getProducts(filters: DatabaseFilters = {}): Promise<Product[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM products WHERE 1=1';
    const params: any[] = [];

    if (filters.isActive !== undefined) {
      query += ' AND isActive = ?';
      params.push(filters.isActive ? 1 : 0);
    }

    if (filters.category) {
      query += ' AND category = ?';
      params.push(filters.category);
    }

    if (filters.search) {
      query += ' AND (name LIKE ? OR description LIKE ? OR sku LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY name ASC';

    const result = await this.db.getAllAsync(query, params) as ParsedProduct[];
    return result.map(this.parseProduct);
  }

  async getProductById(id: number): Promise<Product | null> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getFirstAsync('SELECT * FROM products WHERE id = ?', [id]) as ParsedProduct | null;
    return result ? this.parseProduct(result) : null;
  }

  async saveProduct(product: Partial<Product> & { name: string; price: number }): Promise<Product> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    if (product.id) {
      // Update existing product
      await this.db.runAsync(`
        UPDATE products SET
          name = ?, description = ?, price = ?, cost = ?, stock = ?,
          sku = ?, barcode = ?, category = ?, image = ?, isActive = ?,
          isFeatured = ?, tags = ?, updatedAt = ?
        WHERE id = ?
      `, [
        product.name, product.description || '', product.price, (product as any).cost || 0, product.stock || 0,
        product.sku || '', product.barcode || '', product.category || '', product.image || '',
        product.isActive ? 1 : 0, (product as any).isFeatured ? 1 : 0,
        JSON.stringify((product as any).tags || []), now, product.id
      ]);
      return { ...product, updatedAt: now } as Product;
    } else {
      // Insert new product
      const result = await this.db.runAsync(`
        INSERT INTO products (
          name, description, price, cost, stock, sku, barcode, category,
          image, isActive, isFeatured, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        product.name, product.description || '', product.price, (product as any).cost || 0, product.stock || 0,
        product.sku || '', product.barcode || '', product.category || '', product.image || '',
        product.isActive ? 1 : 0, (product as any).isFeatured ? 1 : 0,
        JSON.stringify((product as any).tags || []), now, now
      ]);

      return { 
        ...product, 
        id: result.lastInsertRowId!.toString(), 
        createdAt: now, 
        updatedAt: now 
      } as Product;
    }
  }

  async deleteProduct(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM products WHERE id = ?', [id]);
  }

  // Order operations
  async getOrders(filters: DatabaseFilters = {}): Promise<Order[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.status) {
      query += ' AND o.status = ?';
      params.push(filters.status);
    }

    if (filters.dateFrom) {
      query += ' AND o.date >= ?';
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      query += ' AND o.date <= ?';
      params.push(filters.dateTo);
    }

    if (filters.search) {
      query += ' AND (o.customerName LIKE ? OR o.id LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    query += ' GROUP BY o.id ORDER BY o.createdAt DESC';

    const result = await this.db.getAllAsync(query, params) as ParsedOrder[];
    return result.map(this.parseOrder);
  }

  async getOrderById(id: string): Promise<Order | null> {
    if (!this.db) throw new Error('Database not initialized');

    const query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE o.id = ?
      GROUP BY o.id
    `;

    const result = await this.db.getFirstAsync(query, [id]) as ParsedOrder | null;
    return result ? this.parseOrder(result) : null;
  }

  async saveOrder(order: Partial<Order> & { id: string; customerName: string; total: number }): Promise<Order> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    await this.db.withTransactionAsync(async () => {
      if (order.id && await this.getOrderById(order.id)) {
        // Update existing order
        await this.db!.runAsync(`
          UPDATE orders SET
            customerName = ?, customer = ?, email = ?, phone = ?, date = ?, time = ?,
            status = ?, orderType = ?, subtotal = ?, tax = ?, discount = ?, total = ?,
            notes = ?, image = ?, updatedAt = ?
          WHERE id = ?
        `, [
          order.customerName, order.customer || '', order.email || '', order.phone || '',
          order.date || '', order.time || '', order.status || 'pending', order.orderType || 'pickup',
          order.subtotal || 0, order.tax || 0, order.discount || 0, order.total,
          order.notes || '', order.image || '', now, order.id
        ]);

        // Delete existing order items
        await this.db!.runAsync('DELETE FROM order_items WHERE orderId = ?', [order.id]);
      } else {
        // Insert new order
        await this.db!.runAsync(`
          INSERT INTO orders (
            id, customerName, customer, email, phone, date, time, status,
            orderType, subtotal, tax, discount, total, notes, image, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          order.id, order.customerName, order.customer || '', order.email || '', order.phone || '',
          order.date || '', order.time || '', order.status || 'pending', order.orderType || 'pickup',
          order.subtotal || 0, order.tax || 0, order.discount || 0, order.total,
          order.notes || '', order.image || '', now, now
        ]);
      }

      // Insert order items
      if (order.items && order.items.length > 0) {
        for (const item of order.items) {
          await this.db!.runAsync(`
            INSERT INTO order_items (orderId, productId, productName, quantity, price, total)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [order.id, item.productId, item.productName, item.quantity, item.price, item.total]);
        }
      }
    });

    return { ...order, updatedAt: now } as Order;
  }

  async deleteOrder(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.withTransactionAsync(async () => {
      // Delete order items first (foreign key constraint)
      await this.db!.runAsync('DELETE FROM order_items WHERE orderId = ?', [id]);
      // Delete the order
      await this.db!.runAsync('DELETE FROM orders WHERE id = ?', [id]);
    });
  }

  // Customer operations
  async getCustomers(): Promise<Customer[]> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.getAllAsync('SELECT * FROM customers ORDER BY name ASC') as ParsedCustomer[];
    return result.map(row => ({
      ...row,
      isVIP: Boolean(row.isVIP),
      tags: row.tags ? JSON.parse(row.tags) : []
    } as Customer));
  }

  async saveCustomer(customer: Partial<Customer> & { name: string }): Promise<Customer> {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();

    if (customer.id) {
      await this.db.runAsync(`
        UPDATE customers SET
          name = ?, email = ?, phone = ?, address = ?, notes = ?,
          isVIP = ?, totalOrders = ?, totalSpent = ?, tags = ?, updatedAt = ?
        WHERE id = ?
      `, [
        customer.name, customer.email || '', customer.phone || '', customer.address || '', (customer as any).notes || '',
        (customer as any).isVIP ? 1 : 0, customer.totalOrders || 0, customer.totalSpent || 0,
        JSON.stringify((customer as any).tags || []), now, customer.id
      ]);
      return { ...customer, updatedAt: now } as Customer;
    } else {
      const result = await this.db.runAsync(`
        INSERT INTO customers (
          name, email, phone, address, notes, isVIP, totalOrders, totalSpent, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        customer.name, customer.email || '', customer.phone || '', customer.address || '', (customer as any).notes || '',
        (customer as any).isVIP ? 1 : 0, customer.totalOrders || 0, customer.totalSpent || 0,
        JSON.stringify((customer as any).tags || []), now, now
      ]);

      return { 
        ...customer, 
        id: result.lastInsertRowId!.toString(), 
        createdAt: now, 
        updatedAt: now 
      } as Customer;
    }
  }

  async deleteCustomer(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM customers WHERE id = ?', [id]);
  }

  // Helper methods
  private parseProduct(row: ParsedProduct): Product {
    return {
      ...row,
      isActive: Boolean(row.isActive),
      isFeatured: Boolean(row.isFeatured),
      tags: row.tags ? JSON.parse(row.tags) : []
    } as Product;
  }

  private parseOrder(row: ParsedOrder): Order {
    try {
      let items: OrderItem[] = [];
      if (row.items && row.items !== null) {
        // Handle GROUP_CONCAT result from SQLite
        if (typeof row.items === 'string') {
          if (row.items.startsWith('[')) {
            // Already a JSON array string
            items = JSON.parse(row.items);
          } else {
            // GROUP_CONCAT result - wrap in array and parse
            const wrappedItems = `[${row.items}]`;
            items = JSON.parse(wrappedItems);
          }
        } else if (Array.isArray(row.items)) {
          items = row.items;
        }
      }

      return {
        ...row,
        items: items || []
      } as Order;
    } catch (error) {
      LoggingService.warn('Error parsing order items', 'DATABASE', { error: (error as Error).message, rawItems: row.items });
      // Fallback: try to extract items manually if JSON parsing fails
      try {
        if (row.items && typeof row.items === 'string') {
          // Try to split by JSON object boundaries and parse individually
          const jsonObjects = row.items.match(/\{[^}]*\}/g);
          if (jsonObjects) {
            const parsedItems = jsonObjects.map(obj => JSON.parse(obj));
            return {
              ...row,
              items: parsedItems
            } as Order;
          }
        }
      } catch (fallbackError) {
        LoggingService.warn('Fallback parsing also failed', 'DATABASE', fallbackError);
      }

      return {
        ...row,
        items: []
      } as Order;
    }
  }

  // Migration method to import existing data
  async migrateFromAsyncStorage(data: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    LoggingService.info('Starting SQLite migration', 'DATABASE');

    await this.db.withTransactionAsync(async () => {
      // Migrate products
      if (data.products) {
        for (const product of data.products) {
          await this.saveProduct(product);
        }
        LoggingService.info(`Migrated ${data.products.length} products`, 'DATABASE');
      }

      // Migrate orders
      if (data.orders) {
        for (const order of data.orders) {
          await this.saveOrder(order);
        }
        LoggingService.info(`Migrated ${data.orders.length} orders`, 'DATABASE');
      }

      // Migrate customers
      if (data.customers) {
        for (const customer of data.customers) {
          await this.saveCustomer(customer);
        }
        LoggingService.info(`Migrated ${data.customers.length} customers`, 'DATABASE');
      }
    });

    LoggingService.info('SQLite migration completed successfully', 'DATABASE');
  }

  // Clear all data from all tables
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.withTransactionAsync(async () => {
      // Clear all tables in the correct order (respecting foreign key constraints)
      await this.db!.runAsync('DELETE FROM order_items');
      await this.db!.runAsync('DELETE FROM orders');
      await this.db!.runAsync('DELETE FROM products');
      await this.db!.runAsync('DELETE FROM customers');
      await this.db!.runAsync('DELETE FROM expenses');
      await this.db!.runAsync('DELETE FROM cash_reconciliations');
      await this.db!.runAsync('DELETE FROM settings');

      // Reset auto-increment counters
      await this.db!.runAsync('DELETE FROM sqlite_sequence');
    });

    LoggingService.info('All data cleared from SQLite database', 'DATABASE');
  }
}

export default new SQLiteService();