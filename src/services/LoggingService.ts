import AsyncStorage from '@react-native-async-storage/async-storage';
import { PerformanceMetrics, AppError } from '../types';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  category: string;
  data?: any;
  userId?: string;
  sessionId: string;
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
  performance?: Partial<PerformanceMetrics>;
  error?: AppError;
}

export interface LoggingConfig {
  level: LogLevel;
  enableConsole: boolean;
  enablePersistence: boolean;
  enableRemoteLogging: boolean;
  maxLogEntries: number;
  flushInterval: number;
  remoteEndpoint?: string;
  apiKey?: string;
}

class LoggingService {
  private config: LoggingConfig;
  private sessionId: string;
  private logBuffer: LogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;
  private performanceMetrics: PerformanceMetrics = {
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    errorRate: 0,
    startupTime: 0,
    fps: 0,
    navigationTime: 0,
  };
  // Track last log time for each metric
  private static lastMetricLogTimes: Record<string, number> = {};

  constructor() {
    this.config = {
      level: __DEV__ ? LogLevel.DEBUG : LogLevel.INFO,
      enableConsole: __DEV__,
      enablePersistence: true,
      enableRemoteLogging: !__DEV__,
      maxLogEntries: 1000,
      flushInterval: 30000, // 30 seconds
    };
    
    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    
    this.flushTimer = setInterval(() => {
      this.flushLogs();
    }, this.config.flushInterval);
  }

  public configure(config: Partial<LoggingConfig>): void {
    this.config = { ...this.config, ...config };
    this.startFlushTimer();
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    category: string,
    data?: any,
    error?: AppError
  ): LogEntry {
    return {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      level,
      message,
      category,
      data,
      sessionId: this.sessionId,
      deviceInfo: {
        platform: 'react-native',
        version: '1.0.0',
      },
      performance: { ...this.performanceMetrics },
      error,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    // Maintain buffer size
    if (this.logBuffer.length > this.config.maxLogEntries) {
      this.logBuffer = this.logBuffer.slice(-this.config.maxLogEntries);
    }
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;

    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const levelName = LogLevel[entry.level];
    const prefix = `[${timestamp}] [${levelName}] [${entry.category}]`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, entry.message, entry.error || entry.data || '');
        break;
    }
  }

  public debug(message: string, category: string = 'APP', data?: any): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    
    const entry = this.createLogEntry(LogLevel.DEBUG, message, category, data);
    this.addToBuffer(entry);
    this.logToConsole(entry);
  }

  public info(message: string, category: string = 'APP', data?: any): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    
    const entry = this.createLogEntry(LogLevel.INFO, message, category, data);
    this.addToBuffer(entry);
    this.logToConsole(entry);
  }

  public warn(message: string, category: string = 'APP', data?: any): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    
    const entry = this.createLogEntry(LogLevel.WARN, message, category, data);
    this.addToBuffer(entry);
    this.logToConsole(entry);
  }

  public error(message: string, category: string = 'APP', error?: Error | AppError, data?: any): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    
    const appError: AppError = error instanceof Error ? {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      details: error.stack,
      timestamp: new Date().toISOString(),
    } : error || {
      code: 'GENERIC_ERROR',
      message,
      timestamp: new Date().toISOString(),
    };

    const entry = this.createLogEntry(LogLevel.ERROR, message, category, data, appError);
    this.addToBuffer(entry);
    this.logToConsole(entry);
    
    // Update error rate metric
    this.updatePerformanceMetric('errorRate', this.performanceMetrics.errorRate + 1);
  }

  public fatal(message: string, category: string = 'APP', error?: Error | AppError, data?: any): void {
    const appError: AppError = error instanceof Error ? {
      code: 'FATAL_ERROR',
      message: error.message,
      details: error.stack,
      timestamp: new Date().toISOString(),
    } : error || {
      code: 'FATAL_ERROR',
      message,
      timestamp: new Date().toISOString(),
    };

    const entry = this.createLogEntry(LogLevel.FATAL, message, category, data, appError);
    this.addToBuffer(entry);
    this.logToConsole(entry);
    
    // Immediately flush fatal errors
    this.flushLogs();
  }

  public updatePerformanceMetric(metric: keyof PerformanceMetrics, value: number): void {
    this.performanceMetrics[metric] = value;
    
    const now = Date.now();
    const lastLog = LoggingService.lastMetricLogTimes[metric] || 0;
    if (now - lastLog >= 10000) { // 10 seconds
    this.debug(`Performance metric updated: ${metric} = ${value}`, 'PERFORMANCE', {
      metric,
      value,
        timestamp: new Date().toISOString(),
    });
      LoggingService.lastMetricLogTimes[metric] = now;
    }
  }

  public trackUserAction(action: string, data?: any): void {
    this.info(`User action: ${action}`, 'USER_ACTION', data);
  }

  public trackPageView(pageName: string, loadTime?: number): void {
    const data: any = { pageName };
    if (loadTime) {
      data.loadTime = loadTime;
      this.updatePerformanceMetric('loadTime', loadTime);
    }
    
    this.info(`Page view: ${pageName}`, 'NAVIGATION', data);
  }

  public trackApiCall(endpoint: string, method: string, duration: number, success: boolean): void {
    const data = {
      endpoint,
      method,
      duration,
      success,
    };
    
    if (success) {
      this.info(`API call successful: ${method} ${endpoint}`, 'API', data);
    } else {
      this.error(`API call failed: ${method} ${endpoint}`, 'API', undefined, data);
    }
  }

  public async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // Persist logs locally
      if (this.config.enablePersistence) {
        await this.persistLogs(logsToFlush);
      }

      // Send logs to remote endpoint
      if (this.config.enableRemoteLogging && this.config.remoteEndpoint) {
        await this.sendLogsToRemote(logsToFlush);
      }
    } catch (error) {
      // Re-add logs to buffer if flushing fails
      this.logBuffer.unshift(...logsToFlush);
      console.error('Failed to flush logs:', error);
    }
  }

  private async persistLogs(logs: LogEntry[]): Promise<void> {
    try {
      const existingLogs = await AsyncStorage.getItem('app_logs');
      const allLogs = existingLogs ? JSON.parse(existingLogs) : [];
      
      allLogs.push(...logs);
      
      // Keep only recent logs to prevent storage overflow
      const recentLogs = allLogs.slice(-this.config.maxLogEntries);
      
      await AsyncStorage.setItem('app_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Failed to persist logs:', error);
    }
  }

  private async sendLogsToRemote(logs: LogEntry[]): Promise<void> {
    if (!this.config.remoteEndpoint || !this.config.apiKey) return;

    try {
      const response = await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          sessionId: this.sessionId,
          logs,
          deviceInfo: {
            platform: 'react-native',
            version: '1.0.0',
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Remote logging failed: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to send logs to remote:', error);
      throw error;
    }
  }

  public async getLogs(limit?: number): Promise<LogEntry[]> {
    try {
      const logs = await AsyncStorage.getItem('app_logs');
      const allLogs: LogEntry[] = logs ? JSON.parse(logs) : [];
      
      return limit ? allLogs.slice(-limit) : allLogs;
    } catch (error) {
      console.error('Failed to get logs:', error);
      return [];
    }
  }

  public async clearLogs(): Promise<void> {
    try {
      await AsyncStorage.removeItem('app_logs');
      this.logBuffer = [];
      this.info('Logs cleared', 'LOGGING');
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushLogs();
  }
}

export default new LoggingService();
